"use client"

import { motion } from "framer-motion"
import { Mail, ArrowR<PERSON>, <PERSON> } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function BlogNewsletter() {
  return (
    <section className="py-20 bg-gradient-to-r from-purple-900/50 to-pink-900/50 border-t border-slate-800">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm"
            >
              <Bell className="w-4 h-4 mr-2 text-purple-400" />
              <span className="text-purple-300 text-sm font-medium">Weekly SEO Insights</span>
            </motion.div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-white">
              Never Miss an{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Update
              </span>
            </h2>
            
            <p className="text-xl text-slate-400 max-w-2xl mx-auto">
              Get the latest SEO strategies, industry news, and exclusive insights delivered straight to your inbox every week.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
          >
            <div className="flex-1 relative">
              <Input
                placeholder="Enter your email address..."
                className="h-12 text-lg bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pr-12 backdrop-blur-sm"
              />
              <Mail className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-500" />
            </div>
            <Button className="h-12 px-6 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
              Subscribe
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </motion.div>

          <p className="text-sm text-slate-500">
            Join 50,000+ SEO professionals • Unsubscribe anytime • No spam, ever
          </p>
        </motion.div>
      </div>
    </section>
  )
}
