"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON><PERSON>, <PERSON>, Heart } from 'lucide-react'

export function AboutMission() {
  return (
    <section className="py-20 bg-slate-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-4">
              <h2 className="text-4xl md:text-5xl font-bold text-white">
                Our{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Story
                </span>
              </h2>
              <p className="text-xl text-slate-400 leading-relaxed">
                Founded in 2020 by a team of SEO veterans who were frustrated with the complexity and cost of existing tools, LinkNest was born from a simple idea: make powerful SEO analysis accessible to everyone.
              </p>
              <p className="text-lg text-slate-400 leading-relaxed">
                What started as a side project has grown into the world's most trusted link analysis platform, serving over 250,000 businesses worldwide and processing billions of data points daily.
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {[
              {
                icon: Lightbulb,
                title: "Innovation First",
                description: "We constantly push the boundaries of what's possible in SEO analysis, leveraging AI and machine learning to provide unprecedented insights."
              },
              {
                icon: Rocket,
                title: "Growth Focused",
                description: "Every feature we build is designed with one goal in mind: helping our customers achieve sustainable, long-term growth."
              },
              {
                icon: Heart,
                title: "Customer Obsessed",
                description: "Our customers' success is our success. We listen, learn, and iterate based on real feedback from real users."
              }
            ].map((item, index) => (
              <motion.div
                key={item.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                viewport={{ once: true }}
                className="flex items-start space-x-4"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <item.icon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">{item.title}</h3>
                  <p className="text-slate-400 leading-relaxed">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
