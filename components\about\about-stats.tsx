"use client"

import { motion } from "framer-motion"
import { TrendingUp, Users, Globe, Award, Zap, Target } from 'lucide-react'

const stats = [
  {
    icon: Users,
    value: "250K+",
    label: "Active Users",
    description: "Businesses trust us",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Globe,
    value: "15B+",
    label: "URLs Analyzed",
    description: "Data points processed",
    color: "from-cyan-500 to-blue-500"
  },
  {
    icon: TrendingUp,
    value: "340%",
    label: "Avg Growth",
    description: "Customer traffic increase",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Award,
    value: "4.9/5",
    label: "Customer Rating",
    description: "Satisfaction score",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Zap,
    value: "99.9%",
    label: "Uptime",
    description: "Service reliability",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Target,
    value: "2.1s",
    label: "Response Time",
    description: "Average analysis speed",
    color: "from-indigo-500 to-purple-500"
  }
]

export function AboutStats() {
  return (
    <section className="py-20 bg-gradient-to-r from-purple-900/20 to-pink-900/20 border-t border-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            LinkNest by the{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Numbers
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            Our impact on the SEO industry and the businesses we serve
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="group text-center"
            >
              <div className="relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-xl rounded-2xl p-6 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-300">
                <div className={`absolute inset-0 bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}></div>
                
                <div className="relative z-10 space-y-4">
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                    className={`inline-flex items-center justify-center w-14 h-14 bg-gradient-to-r ${stat.color} rounded-2xl shadow-lg`}
                  >
                    <stat.icon className="w-7 h-7 text-white" />
                  </motion.div>
                  
                  <div>
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.3, duration: 0.6, type: "spring" }}
                      viewport={{ once: true }}
                      className="text-3xl font-bold text-white mb-1"
                    >
                      {stat.value}
                    </motion.div>
                    <div className="text-lg font-semibold text-slate-300 mb-1">
                      {stat.label}
                    </div>
                    <div className="text-sm text-slate-500">
                      {stat.description}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
