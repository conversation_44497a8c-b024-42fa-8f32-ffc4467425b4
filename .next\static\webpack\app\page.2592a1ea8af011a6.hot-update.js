"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/testimonials.tsx":
/*!*************************************!*\
  !*** ./components/testimonials.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Testimonials: () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Quote,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* __next_internal_client_entry_do_not_use__ Testimonials auto */ \n\n\n\n\nconst testimonials = [\n    {\n        name: \"Sarah Chen\",\n        role: \"SEO Director\",\n        company: \"TechFlow Inc\",\n        avatar: \"/diverse-woman-portrait.png\",\n        content: \"LinkNest transformed our SEO strategy completely. We saw a 340% increase in organic traffic within 6 months. The competitor analysis feature is absolutely game-changing!\",\n        rating: 5,\n        metrics: {\n            traffic: \"+340%\",\n            rankings: \"+127%\",\n            leads: \"+89%\"\n        }\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"Digital Marketing Manager\",\n        company: \"GrowthLab\",\n        avatar: \"/thoughtful-man.png\",\n        content: \"The real-time monitoring and AI-powered insights helped us identify and fix critical SEO issues before they impacted our rankings. ROI increased by 250%!\",\n        rating: 5,\n        metrics: {\n            roi: \"+250%\",\n            backlinks: \"+180%\",\n            authority: \"+45%\"\n        }\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"Content Strategy Lead\",\n        company: \"ContentPro\",\n        avatar: \"/diverse-woman-portrait.png\",\n        content: \"LinkNest's toxic link detection saved our website from a major penalty. The automated reporting feature saves us 20+ hours per week. Absolutely essential tool!\",\n        rating: 5,\n        metrics: {\n            time: \"20h saved\",\n            penalty: \"0 issues\",\n            growth: \"+156%\"\n        }\n    }\n];\nfunction Testimonials() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                                children: [\n                                    \"Success Stories from\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                        children: \"Industry Leaders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-400 max-w-3xl mx-auto\",\n                                children: \"See how top companies are crushing their competition with LinkNest\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-3 gap-8\",\n                        children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.2,\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -10\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    className: \"h-full bg-gradient-to-br from-slate-800/80 via-slate-850/90 to-slate-900/95 backdrop-blur-xl border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500 overflow-hidden relative\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',\n                                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-tr from-cyan-500/3 via-transparent to-blue-500/3 opacity-0 group-hover:opacity-100 transition-opacity duration-700 rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                            className: \"p-8 h-full flex flex-col relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-6\",\n                                                    children: [\n                                                        ...Array(testimonial.rating)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0\n                                                            },\n                                                            whileInView: {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                delay: index * 0.2 + i * 0.1,\n                                                                duration: 0.3\n                                                            },\n                                                            viewport: {\n                                                                once: true\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-5 h-5 text-yellow-400 fill-current\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, i, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-6 flex-grow\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-8 h-8 text-purple-400 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-300 leading-relaxed text-lg group-hover:text-white transition-colors\",\n                                                            children: testimonial.content\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-4 mb-6 p-4 bg-slate-800/30 rounded-2xl border border-slate-700/30\",\n                                                    children: Object.entries(testimonial.metrics).map((param, i)=>{\n                                                        let [key, value] = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-lg font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                                                    children: value\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                    lineNumber: 115,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-slate-500 capitalize\",\n                                                                    children: key\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                                                    className: \"w-12 h-12 mr-4 ring-2 ring-purple-500/20\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                                            src: testimonial.avatar || \"/placeholder.svg\",\n                                                                            alt: testimonial.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                            lineNumber: 127,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                                            className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white\",\n                                                                            children: testimonial.name.split(' ').map((n)=>n[0]).join('')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-white\",\n                                                                            children: testimonial.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                            lineNumber: 133,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-slate-400\",\n                                                                            children: testimonial.role\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                            lineNumber: 134,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-purple-400\",\n                                                                            children: testimonial.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                            lineNumber: 135,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                            whileHover: {\n                                                                x: 5\n                                                            },\n                                                            className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-5 h-5 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this)\n                            }, testimonial.name, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\testimonials.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c = Testimonials;\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/testimonials.tsx\n"));

/***/ })

});