"use client"

import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON>er } from "@/components/footer"
import { BlogHero } from "@/components/blog/blog-hero"
import { BlogGrid } from "@/components/blog/blog-grid"
import { BlogCategories } from "@/components/blog/blog-categories"
import { BlogNewsletter } from "@/components/blog/blog-newsletter"

export default function BlogPage() {
  return (
    <main className="min-h-screen bg-slate-950 overflow-x-hidden">
      <Header />
      <BlogHero />
      <BlogCategories />
      <BlogGrid />
      <BlogNewsletter />
      <Footer />
    </main>
  )
}
