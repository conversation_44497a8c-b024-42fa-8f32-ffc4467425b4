"use client"

import React from "react"
import { motion } from "framer-motion"
import { Check, Zap, Crown, Rocket, ArrowRight, Sparkles, Star } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

const plans = [
  {
    name: "Starter",
    price: "Free",
    period: "Forever",
    description: "Perfect for small websites and beginners",
    icon: Zap,
    color: "from-green-500 to-emerald-500",
    features: [
      "100 backlinks analysis per month",
      "Basic competitor research",
      "Link quality scoring",
      "Email support",
      "Basic reporting",
    ],
    limitations: [
      "Limited to 1 website",
      "Basic metrics only",
    ],
    cta: "Get Started Free",
    popular: false
  },
  {
    name: "Professional",
    price: "$49",
    period: "per month",
    description: "Ideal for growing businesses and agencies",
    icon: Crown,
    color: "from-purple-500 to-pink-500",
    features: [
      "10,000 backlinks analysis per month",
      "Advanced competitor intelligence",
      "Toxic link detection",
      "Real-time monitoring",
      "White-label reports",
      "API access",
      "Priority support",
      "Custom alerts",
    ],
    limitations: [],
    cta: "Start 14-Day Trial",
    popular: true
  },
  {
    name: "Enterprise",
    price: "$199",
    period: "per month",
    description: "For large organizations with complex needs",
    icon: Rocket,
    color: "from-orange-500 to-red-500",
    features: [
      "Unlimited backlinks analysis",
      "Advanced AI insights",
      "Custom integrations",
      "Dedicated account manager",
      "Custom reporting",
      "Team collaboration tools",
      "Advanced security",
      "SLA guarantee",
      "Custom training",
    ],
    limitations: [],
    cta: "Contact Sales",
    popular: false
  }
]

export function Pricing() {
  return (
    <section id="pricing" className="py-20 bg-slate-950 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Choose Your{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Growth Plan
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            Start free and scale as you grow. All plans include our core features with no hidden fees.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -10 }}
              className={`relative group ${plan.popular ? 'lg:-mt-8' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                  <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </div>
                </div>
              )}

              <Card className={`h-full bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 ${plan.popular ? 'border-purple-500/50 shadow-2xl shadow-purple-500/20' : ''}`}>
                <CardHeader className="text-center pb-8">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className={`w-16 h-16 bg-gradient-to-r ${plan.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}
                  >
                    <plan.icon className="w-8 h-8 text-white" />
                  </motion.div>
                  
                  <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                  <p className="text-slate-400 mb-4">{plan.description}</p>
                  
                  <div className="space-y-2">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-white">{plan.price}</span>
                      {plan.price !== "Free" && (
                        <span className="text-slate-400 ml-2">/{plan.period.split(' ')[1]}</span>
                      )}
                    </div>
                    <p className="text-sm text-slate-500">{plan.period}</p>
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    {plan.features.map((feature, i) => (
                      <div key={i} className="flex items-center">
                        <div className={`w-5 h-5 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mr-3 flex-shrink-0`}>
                          <Check className="w-3 h-3 text-white" />
                        </div>
                        <span className="text-slate-300">{feature}</span>
                      </div>
                    ))}
                    
                    {plan.limitations.map((limitation, i) => (
                      <div key={i} className="flex items-center opacity-60">
                        <div className="w-5 h-5 bg-slate-600 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                          <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                        </div>
                        <span className="text-slate-400 text-sm">{limitation}</span>
                      </div>
                    ))}
                  </div>

                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button 
                      className={`w-full h-12 font-semibold ${
                        plan.popular 
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg shadow-purple-500/25' 
                          : 'bg-slate-800 hover:bg-slate-700 text-white border border-slate-600'
                      }`}
                    >
                      {plan.cta}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-slate-400 mb-4">
            All plans include 14-day free trial • No setup fees • Cancel anytime
          </p>
          <div className="flex items-center justify-center space-x-8 text-sm text-slate-500">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              99.9% Uptime SLA
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
              24/7 Support
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
              SOC 2 Compliant
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
