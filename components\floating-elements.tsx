"use client"

import React from "react"
import { motion } from "framer-motion"
import { <PERSON>rkles, Zap, Target, Shield, Globe, TrendingUp } from 'lucide-react'

const floatingElements = [
  {
    icon: Sparkles,
    color: "from-purple-500 to-pink-500",
    size: "w-12 h-12",
    position: { top: "10%", left: "5%" },
    delay: 0,
  },
  {
    icon: Zap,
    color: "from-cyan-500 to-blue-500",
    size: "w-8 h-8",
    position: { top: "20%", right: "10%" },
    delay: 1,
  },
  {
    icon: Target,
    color: "from-green-500 to-emerald-500",
    size: "w-10 h-10",
    position: { top: "60%", left: "8%" },
    delay: 2,
  },
  {
    icon: Shield,
    color: "from-orange-500 to-red-500",
    size: "w-14 h-14",
    position: { bottom: "20%", right: "5%" },
    delay: 3,
  },
  {
    icon: Globe,
    color: "from-indigo-500 to-purple-500",
    size: "w-6 h-6",
    position: { top: "40%", left: "15%" },
    delay: 4,
  },
  {
    icon: TrendingUp,
    color: "from-pink-500 to-rose-500",
    size: "w-16 h-16",
    position: { bottom: "40%", left: "10%" },
    delay: 5,
  },
]

export function FloatingElements() {
  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {floatingElements.map((element, index) => (
        <motion.div
          key={index}
          className="absolute"
          style={element.position}
          initial={{ opacity: 0, scale: 0, rotate: -180 }}
          animate={{ 
            opacity: [0, 0.6, 0.3, 0.8, 0.2],
            scale: [0, 1.2, 0.8, 1.5, 0.6],
            rotate: [0, 180, 360, 540, 720],
            y: [-20, 20, -30, 15, -25],
            x: [-10, 15, -20, 10, -15],
          }}
          transition={{
            duration: 8 + index * 2,
            repeat: Infinity,
            delay: element.delay,
            ease: "easeInOut",
          }}
        >
          <motion.div
            className={`${element.size} bg-gradient-to-r ${element.color} rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/10`}
            style={{
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
              filter: 'blur(0.5px)',
            }}
            whileHover={{ scale: 1.2, rotate: 45 }}
          >
            <element.icon className="w-1/2 h-1/2 text-white" />
          </motion.div>
          
          {/* Glow effect */}
          <div 
            className={`absolute inset-0 bg-gradient-to-r ${element.color} rounded-2xl blur-lg opacity-50 scale-110`}
          />
        </motion.div>
      ))}
      
      {/* Additional floating particles */}
      {[...Array(15)].map((_, i) => (
        <motion.div
          key={`particle-${i}`}
          className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [-30, -80, -30],
            x: [-20, 20, -20],
            opacity: [0, 1, 0],
            scale: [0, 1.5, 0],
          }}
          transition={{
            duration: 4 + Math.random() * 3,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  )
}
