"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/features.tsx":
/*!*********************************!*\
  !*** ./components/features.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Features: () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* __next_internal_client_entry_do_not_use__ Features auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Advanced Link Discovery\",\n        description: \"Uncover hidden link opportunities with our proprietary crawling technology that analyzes millions of websites daily.\",\n        stats: \"50M+ links discovered\",\n        color: \"from-purple-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Comprehensive Reporting\",\n        description: \"Generate detailed reports with actionable insights, custom metrics, and white-label options for agencies.\",\n        stats: \"200+ metrics tracked\",\n        color: \"from-cyan-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Toxic Link Detection\",\n        description: \"Protect your site from harmful backlinks with AI-powered toxic link identification and disavow file generation.\",\n        stats: \"99.8% accuracy rate\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lightning Fast Analysis\",\n        description: \"Get instant results with our high-performance infrastructure that processes millions of data points in seconds.\",\n        stats: \"2.1s avg response time\",\n        color: \"from-orange-500 to-red-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Competitor Intelligence\",\n        description: \"Reverse-engineer your competitors' link building strategies and discover their most valuable backlinks.\",\n        stats: \"10M+ competitors analyzed\",\n        color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Global Database\",\n        description: \"Access the world's largest link database with coverage across 190+ countries and 500+ languages.\",\n        stats: \"15B+ URLs indexed\",\n        color: \"from-pink-500 to-rose-500\"\n    }\n];\nfunction Features() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-32 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            rotate: [\n                                360,\n                                0\n                            ],\n                            scale: [\n                                1.2,\n                                0.8,\n                                1.2\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-300 font-medium\",\n                                        children: \"Powered by AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-black text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Supercharge Your\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent\",\n                                        children: \"SEO Game\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Unlock the full potential of your website with our cutting-edge SEO tools and data-driven insights\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-8\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.02,\n                                    y: -5\n                                },\n                                className: \"group relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r \".concat(feature.color, \" opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            whileHover: {\n                                                                scale: 1.1,\n                                                                rotate: 5\n                                                            },\n                                                            className: \"w-16 h-16 bg-gradient-to-r \".concat(feature.color, \" rounded-2xl flex items-center justify-center shadow-lg\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"w-8 h-8 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-semibold bg-gradient-to-r \".concat(feature.color, \" bg-clip-text text-transparent\"),\n                                                                children: feature.stats\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 leading-relaxed text-lg group-hover:text-slate-300 transition-colors\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-slate-700/50 rounded-full h-1 overflow-hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        whileInView: {\n                                                            width: \"100%\"\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1 + 0.5,\n                                                            duration: 1.5\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"bg-gradient-to-r \".concat(feature.color, \" h-full rounded-full\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 15\n                                }, this)\n                            }, feature.title, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZmVhdHVyZXMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDYTtBQUM0RTtBQUVsSCxNQUFNUyxXQUFXO0lBQ2Y7UUFDRUMsTUFBTVIsNkhBQU1BO1FBQ1pTLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE9BQU87SUFDVDtJQUNBO1FBQ0VKLE1BQU1QLDZIQUFTQTtRQUNmUSxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7SUFDQTtRQUNFSixNQUFNTiw2SEFBTUE7UUFDWk8sT0FBTztRQUNQQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsT0FBTztJQUNUO0lBQ0E7UUFDRUosTUFBTUwsNkhBQUdBO1FBQ1RNLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxPQUFPO1FBQ1BDLE9BQU87SUFDVDtJQUNBO1FBQ0VKLE1BQU1KLDZIQUFNQTtRQUNaSyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxPQUFPO0lBQ1Q7SUFDQTtRQUNFSixNQUFNSCw2SEFBS0E7UUFDWEksT0FBTztRQUNQQyxhQUFhO1FBQ2JDLE9BQU87UUFDUEMsT0FBTztJQUNUO0NBQ0Q7QUFFTSxTQUFTQztJQUNkLHFCQUNFLDhEQUFDQztRQUFRQyxJQUFHO1FBQVdDLFdBQVU7OzBCQUUvQiw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDakIsaURBQU1BLENBQUNrQixHQUFHO3dCQUNUQyxTQUFTOzRCQUNQQyxRQUFRO2dDQUFDO2dDQUFHOzZCQUFJOzRCQUNoQkMsT0FBTztnQ0FBQztnQ0FBRztnQ0FBSzs2QkFBRTt3QkFDcEI7d0JBQ0FDLFlBQVk7NEJBQ1ZDLFVBQVU7NEJBQ1ZDLFFBQVFDOzRCQUNSQyxNQUFNO3dCQUNSO3dCQUNBVCxXQUFVOzs7Ozs7a0NBRVosOERBQUNqQixpREFBTUEsQ0FBQ2tCLEdBQUc7d0JBQ1RDLFNBQVM7NEJBQ1BDLFFBQVE7Z0NBQUM7Z0NBQUs7NkJBQUU7NEJBQ2hCQyxPQUFPO2dDQUFDO2dDQUFLO2dDQUFLOzZCQUFJO3dCQUN4Qjt3QkFDQUMsWUFBWTs0QkFDVkMsVUFBVTs0QkFDVkMsUUFBUUM7NEJBQ1JDLE1BQU07d0JBQ1I7d0JBQ0FULFdBQVU7Ozs7Ozs7Ozs7OzswQkFJZCw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDakIsaURBQU1BLENBQUNrQixHQUFHO3dCQUNUUyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxHQUFHO3dCQUFHO3dCQUM3QkMsYUFBYTs0QkFBRUYsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRTt3QkFDaENQLFlBQVk7NEJBQUVDLFVBQVU7d0JBQUk7d0JBQzVCUSxVQUFVOzRCQUFFQyxNQUFNO3dCQUFLO3dCQUN2QmYsV0FBVTs7MENBRVYsOERBQUNqQixpREFBTUEsQ0FBQ2tCLEdBQUc7Z0NBQ1RTLFNBQVM7b0NBQUVDLFNBQVM7b0NBQUdQLE9BQU87Z0NBQUk7Z0NBQ2xDUyxhQUFhO29DQUFFRixTQUFTO29DQUFHUCxPQUFPO2dDQUFFO2dDQUNwQ0MsWUFBWTtvQ0FBRUMsVUFBVTtvQ0FBS1UsT0FBTztnQ0FBSTtnQ0FDeENGLFVBQVU7b0NBQUVDLE1BQU07Z0NBQUs7Z0NBQ3ZCZixXQUFVOztrREFFViw4REFBQ1YsNkhBQVFBO3dDQUFDVSxXQUFVOzs7Ozs7a0RBQ3BCLDhEQUFDaUI7d0NBQUtqQixXQUFVO2tEQUE4Qjs7Ozs7Ozs7Ozs7OzBDQUdoRCw4REFBQ2tCO2dDQUFHbEIsV0FBVTs7b0NBQWdFO29DQUMzRDtrREFDakIsOERBQUNpQjt3Q0FBS2pCLFdBQVU7a0RBQXlGOzs7Ozs7Ozs7Ozs7MENBSTNHLDhEQUFDbUI7Z0NBQUVuQixXQUFVOzBDQUEyRDs7Ozs7Ozs7Ozs7O2tDQUsxRSw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ1pULFNBQVM2QixHQUFHLENBQUMsQ0FBQ0MsU0FBU0Msc0JBQ3RCLDhEQUFDdkMsaURBQU1BLENBQUNrQixHQUFHO2dDQUVUUyxTQUFTO29DQUFFQyxTQUFTO29DQUFHQyxHQUFHO2dDQUFHO2dDQUM3QkMsYUFBYTtvQ0FBRUYsU0FBUztvQ0FBR0MsR0FBRztnQ0FBRTtnQ0FDaENQLFlBQVk7b0NBQUVXLE9BQU9NLFFBQVE7b0NBQUtoQixVQUFVO2dDQUFJO2dDQUNoRFEsVUFBVTtvQ0FBRUMsTUFBTTtnQ0FBSztnQ0FDdkJRLFlBQVk7b0NBQUVuQixPQUFPO29DQUFNUSxHQUFHLENBQUM7Z0NBQUU7Z0NBQ2pDWixXQUFVOzBDQUVWLDRFQUFDQztvQ0FBSUQsV0FBVTs7c0RBRWIsOERBQUNDOzRDQUFJRCxXQUFXLHFDQUFtRCxPQUFkcUIsUUFBUXpCLEtBQUssRUFBQzs7Ozs7O3NEQUVuRSw4REFBQ0s7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDQztvREFBSUQsV0FBVTs7c0VBQ2IsOERBQUNqQixpREFBTUEsQ0FBQ2tCLEdBQUc7NERBQ1RzQixZQUFZO2dFQUFFbkIsT0FBTztnRUFBS0QsUUFBUTs0REFBRTs0REFDcENILFdBQVcsOEJBQTRDLE9BQWRxQixRQUFRekIsS0FBSyxFQUFDO3NFQUV2RCw0RUFBQ3lCLFFBQVE3QixJQUFJO2dFQUFDUSxXQUFVOzs7Ozs7Ozs7OztzRUFHMUIsOERBQUNDOzREQUFJRCxXQUFVO3NFQUNiLDRFQUFDQztnRUFBSUQsV0FBVywwQ0FBd0QsT0FBZHFCLFFBQVF6QixLQUFLLEVBQUM7MEVBQ3JFeUIsUUFBUTFCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUtwQiw4REFBQ007O3NFQUNDLDhEQUFDdUI7NERBQUd4QixXQUFVO3NFQUNYcUIsUUFBUTVCLEtBQUs7Ozs7OztzRUFFaEIsOERBQUMwQjs0REFBRW5CLFdBQVU7c0VBQ1ZxQixRQUFRM0IsV0FBVzs7Ozs7Ozs7Ozs7OzhEQUt4Qiw4REFBQ087b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNqQixpREFBTUEsQ0FBQ2tCLEdBQUc7d0RBQ1RTLFNBQVM7NERBQUVlLE9BQU87d0RBQUU7d0RBQ3BCWixhQUFhOzREQUFFWSxPQUFPO3dEQUFPO3dEQUM3QnBCLFlBQVk7NERBQUVXLE9BQU9NLFFBQVEsTUFBTTs0REFBS2hCLFVBQVU7d0RBQUk7d0RBQ3REUSxVQUFVOzREQUFFQyxNQUFNO3dEQUFLO3dEQUN2QmYsV0FBVyxvQkFBa0MsT0FBZHFCLFFBQVF6QixLQUFLLEVBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1uRCw4REFBQ0s7NENBQUlELFdBQVU7Ozs7Ozs7Ozs7OzsrQkFsRFpxQixRQUFRNUIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTBEaEM7S0ExSGdCSSIsInNvdXJjZXMiOlsiQzpcXE1vYXogQ29kaW5nIFBsYXlncm91bmRcXGxpbmtuZXN0LXdlYnNpdGVcXGNvbXBvbmVudHNcXGZlYXR1cmVzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gXCJmcmFtZXItbW90aW9uXCJcbmltcG9ydCB7IFNlYXJjaCwgQmFyQ2hhcnQzLCBTaGllbGQsIFphcCwgVGFyZ2V0LCBHbG9iZSwgVXNlcnMsIEF3YXJkLCBTcGFya2xlcywgQXJyb3dVcFJpZ2h0IH0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5jb25zdCBmZWF0dXJlcyA9IFtcbiAge1xuICAgIGljb246IFNlYXJjaCxcbiAgICB0aXRsZTogXCJBZHZhbmNlZCBMaW5rIERpc2NvdmVyeVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlVuY292ZXIgaGlkZGVuIGxpbmsgb3Bwb3J0dW5pdGllcyB3aXRoIG91ciBwcm9wcmlldGFyeSBjcmF3bGluZyB0ZWNobm9sb2d5IHRoYXQgYW5hbHl6ZXMgbWlsbGlvbnMgb2Ygd2Vic2l0ZXMgZGFpbHkuXCIsXG4gICAgc3RhdHM6IFwiNTBNKyBsaW5rcyBkaXNjb3ZlcmVkXCIsXG4gICAgY29sb3I6IFwiZnJvbS1wdXJwbGUtNTAwIHRvLXBpbmstNTAwXCJcbiAgfSxcbiAge1xuICAgIGljb246IEJhckNoYXJ0MyxcbiAgICB0aXRsZTogXCJDb21wcmVoZW5zaXZlIFJlcG9ydGluZ1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlIGRldGFpbGVkIHJlcG9ydHMgd2l0aCBhY3Rpb25hYmxlIGluc2lnaHRzLCBjdXN0b20gbWV0cmljcywgYW5kIHdoaXRlLWxhYmVsIG9wdGlvbnMgZm9yIGFnZW5jaWVzLlwiLFxuICAgIHN0YXRzOiBcIjIwMCsgbWV0cmljcyB0cmFja2VkXCIsXG4gICAgY29sb3I6IFwiZnJvbS1jeWFuLTUwMCB0by1ibHVlLTUwMFwiXG4gIH0sXG4gIHtcbiAgICBpY29uOiBTaGllbGQsXG4gICAgdGl0bGU6IFwiVG94aWMgTGluayBEZXRlY3Rpb25cIixcbiAgICBkZXNjcmlwdGlvbjogXCJQcm90ZWN0IHlvdXIgc2l0ZSBmcm9tIGhhcm1mdWwgYmFja2xpbmtzIHdpdGggQUktcG93ZXJlZCB0b3hpYyBsaW5rIGlkZW50aWZpY2F0aW9uIGFuZCBkaXNhdm93IGZpbGUgZ2VuZXJhdGlvbi5cIixcbiAgICBzdGF0czogXCI5OS44JSBhY2N1cmFjeSByYXRlXCIsXG4gICAgY29sb3I6IFwiZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDBcIlxuICB9LFxuICB7XG4gICAgaWNvbjogWmFwLFxuICAgIHRpdGxlOiBcIkxpZ2h0bmluZyBGYXN0IEFuYWx5c2lzXCIsXG4gICAgZGVzY3JpcHRpb246IFwiR2V0IGluc3RhbnQgcmVzdWx0cyB3aXRoIG91ciBoaWdoLXBlcmZvcm1hbmNlIGluZnJhc3RydWN0dXJlIHRoYXQgcHJvY2Vzc2VzIG1pbGxpb25zIG9mIGRhdGEgcG9pbnRzIGluIHNlY29uZHMuXCIsXG4gICAgc3RhdHM6IFwiMi4xcyBhdmcgcmVzcG9uc2UgdGltZVwiLFxuICAgIGNvbG9yOiBcImZyb20tb3JhbmdlLTUwMCB0by1yZWQtNTAwXCJcbiAgfSxcbiAge1xuICAgIGljb246IFRhcmdldCxcbiAgICB0aXRsZTogXCJDb21wZXRpdG9yIEludGVsbGlnZW5jZVwiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlJldmVyc2UtZW5naW5lZXIgeW91ciBjb21wZXRpdG9ycycgbGluayBidWlsZGluZyBzdHJhdGVnaWVzIGFuZCBkaXNjb3ZlciB0aGVpciBtb3N0IHZhbHVhYmxlIGJhY2tsaW5rcy5cIixcbiAgICBzdGF0czogXCIxME0rIGNvbXBldGl0b3JzIGFuYWx5emVkXCIsXG4gICAgY29sb3I6IFwiZnJvbS1pbmRpZ28tNTAwIHRvLXB1cnBsZS01MDBcIlxuICB9LFxuICB7XG4gICAgaWNvbjogR2xvYmUsXG4gICAgdGl0bGU6IFwiR2xvYmFsIERhdGFiYXNlXCIsXG4gICAgZGVzY3JpcHRpb246IFwiQWNjZXNzIHRoZSB3b3JsZCdzIGxhcmdlc3QgbGluayBkYXRhYmFzZSB3aXRoIGNvdmVyYWdlIGFjcm9zcyAxOTArIGNvdW50cmllcyBhbmQgNTAwKyBsYW5ndWFnZXMuXCIsXG4gICAgc3RhdHM6IFwiMTVCKyBVUkxzIGluZGV4ZWRcIixcbiAgICBjb2xvcjogXCJmcm9tLXBpbmstNTAwIHRvLXJvc2UtNTAwXCJcbiAgfVxuXVxuXG5leHBvcnQgZnVuY3Rpb24gRmVhdHVyZXMoKSB7XG4gIHJldHVybiAoXG4gICAgPHNlY3Rpb24gaWQ9XCJmZWF0dXJlc1wiIGNsYXNzTmFtZT1cInB5LTMyIGJnLWdyYWRpZW50LXRvLWIgZnJvbS1zbGF0ZS05NTAgdmlhLXNsYXRlLTkwMCB0by1zbGF0ZS05NTAgcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICB7LyogM0QgQmFja2dyb3VuZCBFbGVtZW50cyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiPlxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGFuaW1hdGU9e3tcbiAgICAgICAgICAgIHJvdGF0ZTogWzAsIDM2MF0sXG4gICAgICAgICAgICBzY2FsZTogWzEsIDEuMiwgMV0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMjAsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZWFzZTogXCJsaW5lYXJcIlxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMjAgdy05NiBoLTk2IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNTAwLzEwIHRvLXBpbmstNTAwLzEwIHJvdW5kZWQtZnVsbCBibHVyLTN4bFwiXG4gICAgICAgIC8+XG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgYW5pbWF0ZT17e1xuICAgICAgICAgICAgcm90YXRlOiBbMzYwLCAwXSxcbiAgICAgICAgICAgIHNjYWxlOiBbMS4yLCAwLjgsIDEuMl0sXG4gICAgICAgICAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7XG4gICAgICAgICAgICBkdXJhdGlvbjogMjUsXG4gICAgICAgICAgICByZXBlYXQ6IEluZmluaXR5LFxuICAgICAgICAgICAgZWFzZTogXCJsaW5lYXJcIlxuICAgICAgICAgIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIHJpZ2h0LTIwIHctODAgaC04MCBiZy1ncmFkaWVudC10by1yIGZyb20tY3lhbi01MDAvMTAgdG8tYmx1ZS01MDAvMTAgcm91bmRlZC1mdWxsIGJsdXItM3hsXCJcbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCB9fVxuICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0yMFwiXG4gICAgICAgID5cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCBzY2FsZTogMSB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNiBweS0zIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTUwMC8yMCB0by1waW5rLTUwMC8yMCBib3JkZXIgYm9yZGVyLXB1cnBsZS01MDAvMzAgYmFja2Ryb3AtYmx1ci1zbSBtYi04XCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8U3BhcmtsZXMgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXB1cnBsZS00MDBcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1wdXJwbGUtMzAwIGZvbnQtbWVkaXVtXCI+UG93ZXJlZCBieSBBSTwvc3Bhbj5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC03eGwgZm9udC1ibGFjayB0ZXh0LXdoaXRlIG1iLTYgbGVhZGluZy10aWdodFwiPlxuICAgICAgICAgICAgU3VwZXJjaGFyZ2UgWW91cntcIiBcIn1cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNDAwIHZpYS1waW5rLTQwMCB0by1yZWQtNDAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgIFNFTyBHYW1lXG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtc2xhdGUtNDAwIG1heC13LTN4bCBteC1hdXRvIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgVW5sb2NrIHRoZSBmdWxsIHBvdGVudGlhbCBvZiB5b3VyIHdlYnNpdGUgd2l0aCBvdXIgY3V0dGluZy1lZGdlIFNFTyB0b29scyBhbmQgZGF0YS1kcml2ZW4gaW5zaWdodHNcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbGc6Z3JpZC1jb2xzLTIgZ2FwLThcIj5cbiAgICAgICAgICB7ZmVhdHVyZXMubWFwKChmZWF0dXJlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAga2V5PXtmZWF0dXJlLnRpdGxlfVxuICAgICAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDMwIH19XG4gICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEsIGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHNjYWxlOiAxLjAyLCB5OiAtNSB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCByZWxhdGl2ZVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS04MDAvNTAgdG8tc2xhdGUtOTAwLzgwIGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC0zeGwgcC04IGJvcmRlciBib3JkZXItc2xhdGUtNzAwLzUwIGhvdmVyOmJvcmRlci1wdXJwbGUtNTAwLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTUwMCBoLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICB7LyogR2xvdyBFZmZlY3QgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLXIgJHtmZWF0dXJlLmNvbG9yfSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS01IHJvdW5kZWQtM3hsIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi01MDBgfT48L2Rpdj5cbiAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHotMTAgc3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4xLCByb3RhdGU6IDUgfX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tciAke2ZlYXR1cmUuY29sb3J9IHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ2B9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZmVhdHVyZS5pY29uIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1zbSBmb250LXNlbWlib2xkIGJnLWdyYWRpZW50LXRvLXIgJHtmZWF0dXJlLmNvbG9yfSBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmUuc3RhdHN9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi00IGdyb3VwLWhvdmVyOnRleHQtd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS50aXRsZX1cbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS00MDAgbGVhZGluZy1yZWxheGVkIHRleHQtbGcgZ3JvdXAtaG92ZXI6dGV4dC1zbGF0ZS0zMDAgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBQcm9ncmVzcyBCYXIgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1zbGF0ZS03MDAvNTAgcm91bmRlZC1mdWxsIGgtMSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAgICBpbml0aWFsPXt7IHdpZHRoOiAwIH19XG4gICAgICAgICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgd2lkdGg6IFwiMTAwJVwiIH19XG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkZWxheTogaW5kZXggKiAwLjEgKyAwLjUsIGR1cmF0aW9uOiAxLjUgfX1cbiAgICAgICAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYmctZ3JhZGllbnQtdG8tciAke2ZlYXR1cmUuY29sb3J9IGgtZnVsbCByb3VuZGVkLWZ1bGxgfVxuICAgICAgICAgICAgICAgICAgICA+PC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7LyogRGVjb3JhdGl2ZSBFbGVtZW50cyAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC02IHJpZ2h0LTYgdy0zIGgtMyBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTQwMCB0by1waW5rLTQwMCByb3VuZGVkLWZ1bGwgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L3NlY3Rpb24+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIm1vdGlvbiIsIlNlYXJjaCIsIkJhckNoYXJ0MyIsIlNoaWVsZCIsIlphcCIsIlRhcmdldCIsIkdsb2JlIiwiU3BhcmtsZXMiLCJmZWF0dXJlcyIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwic3RhdHMiLCJjb2xvciIsIkZlYXR1cmVzIiwic2VjdGlvbiIsImlkIiwiY2xhc3NOYW1lIiwiZGl2IiwiYW5pbWF0ZSIsInJvdGF0ZSIsInNjYWxlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwicmVwZWF0IiwiSW5maW5pdHkiLCJlYXNlIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5Iiwid2hpbGVJblZpZXciLCJ2aWV3cG9ydCIsIm9uY2UiLCJkZWxheSIsInNwYW4iLCJoMiIsInAiLCJtYXAiLCJmZWF0dXJlIiwiaW5kZXgiLCJ3aGlsZUhvdmVyIiwiaDMiLCJ3aWR0aCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/features.tsx\n"));

/***/ })

});