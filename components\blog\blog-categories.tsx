"use client"

import { motion } from "framer-motion"
import { TrendingUp, <PERSON>, Search, BarChart3, Shield, Target } from 'lucide-react'

const categories = [
  { name: "All", icon: BarChart3, count: 156, active: true },
  { name: "Link Building", icon: Link, count: 45, active: false },
  { name: "SEO Strategy", icon: Target, count: 38, active: false },
  { name: "Technical SEO", icon: Shield, count: 29, active: false },
  { name: "Analytics", icon: TrendingUp, count: 24, active: false },
  { name: "Local SEO", icon: Search, count: 20, active: false },
]

export function BlogCategories() {
  return (
    <section className="py-12 bg-slate-950 border-b border-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-wrap justify-center gap-4">
          {categories.map((category, index) => (
            <motion.button
              key={category.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -2 }}
              className={`flex items-center space-x-2 px-6 py-3 rounded-2xl transition-all duration-300 ${
                category.active
                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg shadow-purple-500/25'
                  : 'bg-slate-800/50 text-slate-300 hover:bg-slate-700/50 hover:text-white border border-slate-700'
              }`}
            >
              <category.icon className="w-4 h-4" />
              <span className="font-medium">{category.name}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                category.active 
                  ? 'bg-white/20 text-white' 
                  : 'bg-slate-700 text-slate-400'
              }`}>
                {category.count}
              </span>
            </motion.button>
          ))}
        </div>
      </div>
    </section>
  )
}
