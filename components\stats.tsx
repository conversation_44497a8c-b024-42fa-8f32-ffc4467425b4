"use client"

import { motion } from "framer-motion"
import { Users, Globe, TrendingUp, Award, Zap, Target } from 'lucide-react'

const stats = [
  {
    icon: Users,
    value: "250K+",
    label: "Active Users",
    description: "Growing daily",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Globe,
    value: "5M+",
    label: "Sites Analyzed",
    description: "And counting",
    color: "from-cyan-500 to-blue-500"
  },
  {
    icon: TrendingUp,
    value: "99.9%",
    label: "Uptime",
    description: "Always available",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Award,
    value: "4.9/5",
    label: "User Rating",
    description: "Loved by users",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Zap,
    value: "2.5s",
    label: "Avg Response",
    description: "Lightning fast",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Target,
    value: "94%",
    label: "Accuracy Rate",
    description: "Precise results",
    color: "from-indigo-500 to-purple-500"
  }
]

export function Stats() {
  return (
    <section className="py-20 bg-slate-950 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-purple-500/10 via-transparent to-pink-500/10"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Trusted by{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Industry Leaders
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-2xl mx-auto">
            Join thousands of successful businesses using LinkNest to dominate their market
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="group"
            >
              <div
                className="relative backdrop-blur-xl rounded-2xl p-6 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-300"
                style={{
                  background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
                }}
              >
                {/* Enhanced Glow Effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${stat.color} opacity-0 group-hover:opacity-15 rounded-2xl transition-opacity duration-300`}></div>
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 rounded-2xl blur-xl transition-opacity duration-500`}></div>
                
                <div className="relative z-10 text-center space-y-4">
                  <motion.div
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                    className={`inline-flex items-center justify-center w-14 h-14 bg-gradient-to-r ${stat.color} rounded-2xl shadow-lg`}
                  >
                    <stat.icon className="w-7 h-7 text-white" />
                  </motion.div>
                  
                  <div>
                    <motion.div
                      initial={{ scale: 0 }}
                      whileInView={{ scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.3, duration: 0.6, type: "spring" }}
                      viewport={{ once: true }}
                      className="text-3xl font-bold text-white mb-1"
                    >
                      {stat.value}
                    </motion.div>
                    <div className="text-lg font-semibold text-slate-300 mb-1">
                      {stat.label}
                    </div>
                    <div className="text-sm text-slate-500">
                      {stat.description}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
