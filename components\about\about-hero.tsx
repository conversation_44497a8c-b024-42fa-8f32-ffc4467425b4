"use client"

import { motion } from "framer-motion"
import { Target, Users, Globe } from 'lucide-react'

export function AboutHero() {
  return (
    <section className="pt-24 pb-16 bg-slate-950 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm"
            >
              <Target className="w-4 h-4 mr-2 text-purple-400" />
              <span className="text-purple-300 text-sm font-medium">Our Mission</span>
            </motion.div>
            
            <h1 className="text-5xl md:text-7xl font-black text-white leading-tight">
              Revolutionizing{" "}
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
                SEO
              </span>
              <br />
              for Everyone
            </h1>
            
            <p className="text-xl text-slate-400 leading-relaxed max-w-3xl mx-auto">
              We're on a mission to democratize SEO and make advanced link analysis accessible to businesses of all sizes. Founded by SEO experts, built for growth.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto"
          >
            {[
              { icon: Users, label: "250K+", sublabel: "Happy Customers" },
              { icon: Globe, label: "190+", sublabel: "Countries Served" },
              { icon: Target, label: "99.9%", sublabel: "Uptime SLA" }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-white mb-1">{stat.label}</div>
                <div className="text-slate-400">{stat.sublabel}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
