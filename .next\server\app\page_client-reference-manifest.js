globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/bento-grid.tsx":{"*":{"id":"(ssr)/./components/bento-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/cta.tsx":{"*":{"id":"(ssr)/./components/cta.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/features.tsx":{"*":{"id":"(ssr)/./components/features.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(ssr)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(ssr)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hero.tsx":{"*":{"id":"(ssr)/./components/hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pricing.tsx":{"*":{"id":"(ssr)/./components/pricing.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/stats.tsx":{"*":{"id":"(ssr)/./components/stats.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/testimonials.tsx":{"*":{"id":"(ssr)/./components/testimonials.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(ssr)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(ssr)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(ssr)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\bento-grid.tsx":{"id":"(app-pages-browser)/./components/bento-grid.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\cta.tsx":{"id":"(app-pages-browser)/./components/cta.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\features.tsx":{"id":"(app-pages-browser)/./components/features.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\footer.tsx":{"id":"(app-pages-browser)/./components/footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\header.tsx":{"id":"(app-pages-browser)/./components/header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\hero.tsx":{"id":"(app-pages-browser)/./components/hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\pricing.tsx":{"id":"(app-pages-browser)/./components/pricing.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\stats.tsx":{"id":"(app-pages-browser)/./components/stats.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\components\\testimonials.tsx":{"id":"(app-pages-browser)/./components/testimonials.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\app\\about\\page.tsx":{"id":"(app-pages-browser)/./app/about/page.tsx","name":"*","chunks":[],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./app/contact/page.tsx","name":"*","chunks":[],"async":false},"C:\\Moaz Coding Playground\\linknest-website\\app\\blog\\page.tsx":{"id":"(app-pages-browser)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Moaz Coding Playground\\linknest-website\\":[],"C:\\Moaz Coding Playground\\linknest-website\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Moaz Coding Playground\\linknest-website\\app\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/bento-grid.tsx":{"*":{"id":"(rsc)/./components/bento-grid.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/cta.tsx":{"*":{"id":"(rsc)/./components/cta.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/features.tsx":{"*":{"id":"(rsc)/./components/features.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/footer.tsx":{"*":{"id":"(rsc)/./components/footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/header.tsx":{"*":{"id":"(rsc)/./components/header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/hero.tsx":{"*":{"id":"(rsc)/./components/hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pricing.tsx":{"*":{"id":"(rsc)/./components/pricing.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/stats.tsx":{"*":{"id":"(rsc)/./components/stats.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/testimonials.tsx":{"*":{"id":"(rsc)/./components/testimonials.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/about/page.tsx":{"*":{"id":"(rsc)/./app/about/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/contact/page.tsx":{"*":{"id":"(rsc)/./app/contact/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/blog/page.tsx":{"*":{"id":"(rsc)/./app/blog/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}