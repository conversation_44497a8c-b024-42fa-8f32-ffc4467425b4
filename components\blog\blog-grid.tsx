"use client"

import { motion } from "framer-motion"
import { Calendar, Clock, ArrowRight, User } from 'lucide-react'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"

const articles = [
  {
    title: "The Ultimate Guide to Link Building in 2024",
    excerpt: "Discover the most effective link building strategies that actually work in today's competitive landscape.",
    author: "<PERSON>",
    date: "Dec 15, 2024",
    readTime: "8 min read",
    category: "Link Building",
    image: "/placeholder.svg?height=300&width=400&text=Link+Building+Guide",
    featured: true
  },
  {
    title: "How AI is Revolutionizing SEO Analysis",
    excerpt: "Explore how artificial intelligence is transforming the way we approach SEO and link analysis.",
    author: "<PERSON>",
    date: "Dec 12, 2024",
    readTime: "6 min read",
    category: "SEO Strategy",
    image: "/placeholder.svg?height=300&width=400&text=AI+SEO+Analysis",
    featured: false
  },
  {
    title: "Technical SEO Checklist for 2024",
    excerpt: "A comprehensive checklist to ensure your website is technically optimized for search engines.",
    author: "<PERSON>",
    date: "Dec 10, 2024",
    readTime: "12 min read",
    category: "Technical SEO",
    image: "/placeholder.svg?height=300&width=400&text=Technical+SEO",
    featured: false
  },
  {
    title: "Local SEO: Dominating Your Geographic Market",
    excerpt: "Learn how to optimize your business for local search and attract customers in your area.",
    author: "David Kim",
    date: "Dec 8, 2024",
    readTime: "7 min read",
    category: "Local SEO",
    image: "/placeholder.svg?height=300&width=400&text=Local+SEO",
    featured: false
  },
  {
    title: "Understanding Google's Latest Algorithm Updates",
    excerpt: "Stay ahead of the curve with insights into Google's recent algorithm changes and their impact.",
    author: "Lisa Park",
    date: "Dec 5, 2024",
    readTime: "9 min read",
    category: "SEO Strategy",
    image: "/placeholder.svg?height=300&width=400&text=Google+Algorithm",
    featured: false
  },
  {
    title: "Content Marketing and SEO: A Perfect Partnership",
    excerpt: "Discover how to align your content marketing efforts with SEO best practices for maximum impact.",
    author: "James Wilson",
    date: "Dec 3, 2024",
    readTime: "10 min read",
    category: "Content Marketing",
    image: "/placeholder.svg?height=300&width=400&text=Content+Marketing",
    featured: false
  }
]

export function BlogGrid() {
  const featuredArticle = articles.find(article => article.featured)
  const regularArticles = articles.filter(article => !article.featured)

  return (
    <section className="py-20 bg-slate-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Featured Article */}
        {featuredArticle && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-16"
          >
            <Card className="overflow-hidden bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl border-slate-700/50 hover:border-purple-500/30 transition-all duration-500">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-l-xl"></div>
                  <div className="absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-semibold rounded-full">
                    Featured
                  </div>
                </div>
                <CardContent className="p-8 flex flex-col justify-center">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 text-sm text-slate-400">
                      <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full">
                        {featuredArticle.category}
                      </span>
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        {featuredArticle.date}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {featuredArticle.readTime}
                      </div>
                    </div>
                    
                    <h2 className="text-3xl font-bold text-white leading-tight">
                      {featuredArticle.title}
                    </h2>
                    
                    <p className="text-slate-400 text-lg leading-relaxed">
                      {featuredArticle.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between pt-4">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                          <User className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <div className="text-white font-semibold">{featuredArticle.author}</div>
                          <div className="text-slate-400 text-sm">Senior SEO Expert</div>
                        </div>
                      </div>
                      
                      <Button className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
                        Read Article
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Regular Articles Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {regularArticles.map((article, index) => (
            <motion.div
              key={article.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
            >
              <Card className="h-full overflow-hidden bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 group">
                <div className="relative h-48">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10"></div>
                  <div className="absolute top-4 left-4 px-2 py-1 bg-slate-800/80 text-slate-300 text-xs rounded-full">
                    {article.category}
                  </div>
                </div>
                
                <CardContent className="p-6 flex flex-col flex-grow">
                  <div className="space-y-4 flex-grow">
                    <div className="flex items-center space-x-4 text-xs text-slate-400">
                      <div className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {article.date}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        {article.readTime}
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-white leading-tight group-hover:text-purple-300 transition-colors">
                      {article.title}
                    </h3>
                    
                    <p className="text-slate-400 leading-relaxed flex-grow">
                      {article.excerpt}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between pt-4 mt-auto">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-2">
                        <User className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-slate-300 text-sm">{article.author}</span>
                    </div>
                    
                    <Button variant="ghost" size="sm" className="text-purple-400 hover:text-white hover:bg-purple-500/20">
                      Read More
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <Button className="bg-slate-800 hover:bg-slate-700 text-white border border-slate-600 px-8 py-3">
            Load More Articles
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </motion.div>
      </div>
    </section>
  )
}
