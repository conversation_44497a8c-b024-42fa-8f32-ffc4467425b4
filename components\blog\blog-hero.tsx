"use client"

import { motion } from "framer-motion"
import { Search, TrendingUp, BookOpen } from 'lucide-react'
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

export function BlogHero() {
  return (
    <section className="pt-24 pb-16 bg-slate-950 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm"
            >
              <BookOpen className="w-4 h-4 mr-2 text-purple-400" />
              <span className="text-purple-300 text-sm font-medium">SEO Knowledge Hub</span>
            </motion.div>
            
            <h1 className="text-5xl md:text-7xl font-black text-white leading-tight">
              Master{" "}
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
                SEO
              </span>
              <br />
              Excellence
            </h1>
            
            <p className="text-xl text-slate-400 leading-relaxed max-w-2xl mx-auto">
              Discover cutting-edge SEO strategies, link building techniques, and industry insights from our team of experts.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
          >
            <div className="flex-1 relative">
              <Input
                placeholder="Search articles..."
                className="h-12 text-lg bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pr-12 backdrop-blur-sm"
              />
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-500" />
            </div>
            <Button className="h-12 px-6 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white">
              Search
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.8 }}
            className="flex items-center justify-center space-x-8 text-sm text-slate-400"
          >
            <div className="flex items-center">
              <TrendingUp className="w-4 h-4 mr-2 text-purple-400" />
              500+ Articles
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
              Updated Weekly
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
              Expert Authors
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
