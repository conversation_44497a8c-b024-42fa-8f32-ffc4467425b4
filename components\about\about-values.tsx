"use client"

import { motion } from "framer-motion"
import { Shield, Zap, Users, Globe, Award, Target } from 'lucide-react'

const values = [
  {
    icon: Shield,
    title: "Transparency",
    description: "We believe in complete transparency in our methods, pricing, and data sources.",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Zap,
    title: "Speed",
    description: "Fast results shouldn't come at the cost of accuracy. We deliver both.",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Users,
    title: "Community",
    description: "We're building more than a tool - we're fostering a community of SEO professionals.",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: Globe,
    title: "Accessibility",
    description: "Powerful SEO tools should be accessible to businesses of all sizes, everywhere.",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Award,
    title: "Excellence",
    description: "We set the highest standards for ourselves and continuously strive to exceed them.",
    color: "from-indigo-500 to-purple-500"
  },
  {
    icon: Target,
    title: "Results",
    description: "Everything we do is measured by one metric: our customers' success.",
    color: "from-red-500 to-pink-500"
  }
]

export function AboutValues() {
  return (
    <section className="py-20 bg-gradient-to-b from-slate-950 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Our{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Values
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            The principles that guide everything we do and every decision we make
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {values.map((value, index) => (
            <motion.div
              key={value.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-500"
            >
              <div className={`absolute inset-0 bg-gradient-to-r ${value.color} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`}></div>
              
              <div className="relative z-10 text-center space-y-4">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mx-auto shadow-lg`}
                >
                  <value.icon className="w-8 h-8 text-white" />
                </motion.div>
                
                <h3 className="text-2xl font-bold text-white group-hover:text-white transition-colors">
                  {value.title}
                </h3>
                
                <p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors">
                  {value.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
