"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/features.tsx":
/*!*********************************!*\
  !*** ./components/features.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Features: () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpRight,BarChart3,Globe,Search,Shield,Sparkles,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* __next_internal_client_entry_do_not_use__ Features auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Advanced Link Discovery\",\n        description: \"Uncover hidden link opportunities with our proprietary crawling technology that analyzes millions of websites daily.\",\n        stats: \"50M+ links discovered\",\n        color: \"from-purple-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Comprehensive Reporting\",\n        description: \"Generate detailed reports with actionable insights, custom metrics, and white-label options for agencies.\",\n        stats: \"200+ metrics tracked\",\n        color: \"from-cyan-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Toxic Link Detection\",\n        description: \"Protect your site from harmful backlinks with AI-powered toxic link identification and disavow file generation.\",\n        stats: \"99.8% accuracy rate\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lightning Fast Analysis\",\n        description: \"Get instant results with our high-performance infrastructure that processes millions of data points in seconds.\",\n        stats: \"2.1s avg response time\",\n        color: \"from-orange-500 to-red-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Competitor Intelligence\",\n        description: \"Reverse-engineer your competitors' link building strategies and discover their most valuable backlinks.\",\n        stats: \"10M+ competitors analyzed\",\n        color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Global Database\",\n        description: \"Access the world's largest link database with coverage across 190+ countries and 500+ languages.\",\n        stats: \"15B+ URLs indexed\",\n        color: \"from-pink-500 to-rose-500\"\n    }\n];\nfunction Features() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-32 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            rotate: [\n                                0,\n                                360\n                            ],\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 20,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            rotate: [\n                                360,\n                                0\n                            ],\n                            scale: [\n                                1.2,\n                                0.8,\n                                1.2\n                            ]\n                        },\n                        transition: {\n                            duration: 25,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        className: \"absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-purple-300 font-medium\",\n                                        children: \"Powered by AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-5xl md:text-7xl font-black text-white mb-6 leading-tight\",\n                                children: [\n                                    \"Supercharge Your\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent\",\n                                        children: \"SEO Game\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"Unlock the full potential of your website with our cutting-edge SEO tools and data-driven insights\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-8\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 50,\n                                    rotateX: -15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0,\n                                    rotateX: 0\n                                },\n                                transition: {\n                                    delay: index * 0.15,\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.03,\n                                    y: -10,\n                                    rotateX: 5,\n                                    rotateY: 5,\n                                    z: 50\n                                },\n                                className: \"group relative\",\n                                style: {\n                                    transformStyle: 'preserve-3d'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative bg-gradient-to-br from-slate-800/60 to-slate-900/90 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500 h-full overflow-hidden\",\n                                    style: {\n                                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r \".concat(feature.color, \" opacity-0 group-hover:opacity-10 rounded-3xl transition-opacity duration-500\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-br \".concat(feature.color, \" opacity-0 group-hover:opacity-5 rounded-3xl blur-xl transition-opacity duration-500\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"absolute w-1 h-1 bg-gradient-to-r \".concat(feature.color, \" rounded-full\"),\n                                                    style: {\n                                                        left: \"\".concat(20 + i * 15, \"%\"),\n                                                        top: \"\".concat(20 + i * 10, \"%\")\n                                                    },\n                                                    animate: {\n                                                        y: [\n                                                            -10,\n                                                            -20,\n                                                            -10\n                                                        ],\n                                                        opacity: [\n                                                            0,\n                                                            1,\n                                                            0\n                                                        ],\n                                                        scale: [\n                                                            0,\n                                                            1,\n                                                            0\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: 2,\n                                                        repeat: Infinity,\n                                                        delay: i * 0.2,\n                                                        ease: \"easeInOut\"\n                                                    }\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            whileHover: {\n                                                                scale: 1.2,\n                                                                rotate: 15,\n                                                                z: 20\n                                                            },\n                                                            className: \"w-16 h-16 bg-gradient-to-r \".concat(feature.color, \" rounded-2xl flex items-center justify-center shadow-lg relative\"),\n                                                            style: {\n                                                                transformStyle: 'preserve-3d',\n                                                                boxShadow: \"0 10px 30px -5px rgba(168, 85, 247, 0.3)\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                    className: \"w-8 h-8 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-r \".concat(feature.color, \" rounded-2xl blur-lg opacity-50 scale-110\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                    whileHover: {\n                                                                        scale: 1.1\n                                                                    },\n                                                                    className: \"text-sm font-bold bg-gradient-to-r \".concat(feature.color, \" bg-clip-text text-transparent\"),\n                                                                    children: feature.stats\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0\n                                                                    },\n                                                                    whileInView: {\n                                                                        opacity: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: index * 0.1 + 0.5\n                                                                    },\n                                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpRight_BarChart3_Globe_Search_Shield_Sparkles_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-slate-400 mt-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 leading-relaxed text-lg group-hover:text-slate-300 transition-colors\",\n                                                            children: feature.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-slate-700/50 rounded-full h-2 overflow-hidden backdrop-blur-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        initial: {\n                                                            width: 0\n                                                        },\n                                                        whileInView: {\n                                                            width: \"100%\"\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1 + 0.8,\n                                                            duration: 2,\n                                                            ease: \"easeOut\"\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        className: \"bg-gradient-to-r \".concat(feature.color, \" h-full rounded-full relative\"),\n                                                        style: {\n                                                            boxShadow: \"0 0 20px rgba(168, 85, 247, 0.4)\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            animate: {\n                                                                x: [\n                                                                    -20,\n                                                                    20,\n                                                                    -20\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 3,\n                                                                repeat: Infinity\n                                                            },\n                                                            className: \"absolute right-0 top-0 w-4 h-full bg-white/30 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                animate: {\n                                                    scale: [\n                                                        1,\n                                                        1.5,\n                                                        1\n                                                    ]\n                                                },\n                                                transition: {\n                                                    duration: 2,\n                                                    repeat: Infinity\n                                                },\n                                                className: \"w-full h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-6 left-6 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 border-2 border-gradient-to-r \".concat(feature.color, \" rounded-full\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            }, feature.title, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/features.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ArrowUpRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ArrowUpRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ArrowUpRight\", [\n    [\n        \"path\",\n        {\n            d: \"M7 7h10v10\",\n            key: \"1tivn9\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 17 17 7\",\n            key: \"1vkiza\"\n        }\n    ]\n]);\n //# sourceMappingURL=arrow-up-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYXJyb3ctdXAtcmlnaHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFhTSxxQkFBZSxnRUFBZ0IsQ0FBQyxjQUFnQjtJQUNwRDtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBYztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDM0M7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWM7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQzVDIiwic291cmNlcyI6WyJDOlxcc3JjXFxpY29uc1xcYXJyb3ctdXAtcmlnaHQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBBcnJvd1VwUmlnaHRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk55QTNhREV3ZGpFd0lpQXZQZ29nSUR4d1lYUm9JR1E5SWswM0lERTNJREUzSURjaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2Fycm93LXVwLXJpZ2h0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQXJyb3dVcFJpZ2h0ID0gY3JlYXRlTHVjaWRlSWNvbignQXJyb3dVcFJpZ2h0JywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdNNyA3aDEwdjEwJywga2V5OiAnMXRpdm45JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTcgMTcgMTcgNycsIGtleTogJzF2a2l6YScgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgQXJyb3dVcFJpZ2h0O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\n"));

/***/ })

});