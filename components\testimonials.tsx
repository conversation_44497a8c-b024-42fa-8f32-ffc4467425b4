"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from 'lucide-react'
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

const testimonials = [
  {
    name: "<PERSON>",
    role: "SEO Director",
    company: "TechFlow Inc",
    avatar: "/diverse-woman-portrait.png",
    content: "LinkNest transformed our SEO strategy completely. We saw a 340% increase in organic traffic within 6 months. The competitor analysis feature is absolutely game-changing!",
    rating: 5,
    metrics: { traffic: "+340%", rankings: "+127%", leads: "+89%" }
  },
  {
    name: "<PERSON>",
    role: "Digital Marketing Manager",
    company: "GrowthLab",
    avatar: "/thoughtful-man.png",
    content: "The real-time monitoring and AI-powered insights helped us identify and fix critical SEO issues before they impacted our rankings. ROI increased by 250%!",
    rating: 5,
    metrics: { roi: "+250%", backlinks: "+180%", authority: "+45%" }
  },
  {
    name: "<PERSON>",
    role: "Content Strategy Lead",
    company: "ContentPro",
    avatar: "/diverse-woman-portrait.png",
    content: "LinkNest's toxic link detection saved our website from a major penalty. The automated reporting feature saves us 20+ hours per week. Absolutely essential tool!",
    rating: 5,
    metrics: { time: "20h saved", penalty: "0 issues", growth: "+156%" }
  }
]

export function Testimonials() {
  return (
    <section className="py-20 bg-slate-950 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Success Stories from{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Industry Leaders
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            See how top companies are crushing their competition with LinkNest
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -10 }}
              className="group"
            >
              <Card className="h-full bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 overflow-hidden">
                <CardContent className="p-8 h-full flex flex-col">
                  {/* Rating */}
                  <div className="flex items-center mb-6">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ opacity: 0, scale: 0 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.2 + i * 0.1, duration: 0.3 }}
                        viewport={{ once: true }}
                      >
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Quote */}
                  <div className="relative mb-6 flex-grow">
                    <Quote className="w-8 h-8 text-purple-400 mb-4 opacity-50" />
                    <p className="text-slate-300 leading-relaxed text-lg group-hover:text-white transition-colors">
                      {testimonial.content}
                    </p>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-slate-800/30 rounded-2xl border border-slate-700/30">
                    {Object.entries(testimonial.metrics).map(([key, value], i) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                          {value}
                        </div>
                        <div className="text-xs text-slate-500 capitalize">{key}</div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Author */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Avatar className="w-12 h-12 mr-4 ring-2 ring-purple-500/20">
                        <AvatarImage src={testimonial.avatar || "/placeholder.svg"} alt={testimonial.name} />
                        <AvatarFallback className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-white">{testimonial.name}</div>
                        <div className="text-sm text-slate-400">{testimonial.role}</div>
                        <div className="text-xs text-purple-400">{testimonial.company}</div>
                      </div>
                    </div>
                    
                    <motion.div
                      whileHover={{ x: 5 }}
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    >
                      <ArrowRight className="w-5 h-5 text-purple-400" />
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
