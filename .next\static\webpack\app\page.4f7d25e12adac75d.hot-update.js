"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/stats.tsx":
/*!******************************!*\
  !*** ./components/stats.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stats: () => (/* binding */ Stats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ Stats auto */ \n\n\nconst stats = [\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        value: \"250K+\",\n        label: \"Active Users\",\n        description: \"Growing daily\",\n        color: \"from-purple-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        value: \"5M+\",\n        label: \"Sites Analyzed\",\n        description: \"And counting\",\n        color: \"from-cyan-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        value: \"99.9%\",\n        label: \"Uptime\",\n        description: \"Always available\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        value: \"4.9/5\",\n        label: \"User Rating\",\n        description: \"Loved by users\",\n        color: \"from-orange-500 to-red-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        value: \"2.5s\",\n        label: \"Avg Response\",\n        description: \"Lightning fast\",\n        color: \"from-yellow-500 to-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        value: \"94%\",\n        label: \"Accuracy Rate\",\n        description: \"Precise results\",\n        color: \"from-indigo-500 to-purple-500\"\n    }\n];\nfunction Stats() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-slate-950 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-purple-500/10 via-transparent to-pink-500/10\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                                children: [\n                                    \"Trusted by\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                        children: \"Industry Leaders\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-slate-400 max-w-2xl mx-auto\",\n                                children: \"Join thousands of successful businesses using LinkNest to dominate their market\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.1,\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -5\n                                },\n                                className: \"group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative backdrop-blur-xl rounded-2xl p-6 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-300\",\n                                    style: {\n                                        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',\n                                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-r \".concat(stat.color, \" opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative z-10 text-center space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    whileHover: {\n                                                        rotate: 360\n                                                    },\n                                                    transition: {\n                                                        duration: 0.6\n                                                    },\n                                                    className: \"inline-flex items-center justify-center w-14 h-14 bg-gradient-to-r \".concat(stat.color, \" rounded-2xl shadow-lg\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                        className: \"w-7 h-7 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            whileInView: {\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                delay: index * 0.1 + 0.3,\n                                                                duration: 0.6,\n                                                                type: \"spring\"\n                                                            },\n                                                            viewport: {\n                                                                once: true\n                                                            },\n                                                            className: \"text-3xl font-bold text-white mb-1\",\n                                                            children: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-lg font-semibold text-slate-300 mb-1\",\n                                                            children: stat.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-slate-500\",\n                                                            children: stat.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            }, stat.label, false, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\stats.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c = Stats;\nvar _c;\n$RefreshReg$(_c, \"Stats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/stats.tsx\n"));

/***/ })

});