"use client"

import { motion } from "framer-motion"
import { Send, User, Mail, MessageSquare } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function ContactForm() {
  return (
    <section className="py-20 bg-slate-950">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <Card className="bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl border-slate-700/50">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-white mb-2">
                Send us a{" "}
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Message
                </span>
              </CardTitle>
              <p className="text-slate-400">
                We'll get back to you within 24 hours
              </p>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-slate-300">First Name</label>
                  <div className="relative">
                    <Input
                      placeholder="John"
                      className="bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pl-10"
                    />
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-500" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium text-slate-300">Last Name</label>
                  <div className="relative">
                    <Input
                      placeholder="Doe"
                      className="bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pl-10"
                    />
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-500" />
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">Email Address</label>
                <div className="relative">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pl-10"
                  />
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-500" />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">Subject</label>
                <Input
                  placeholder="How can we help you?"
                  className="bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-300">Message</label>
                <div className="relative">
                  <Textarea
                    placeholder="Tell us more about your needs..."
                    rows={6}
                    className="bg-slate-800/50 border-slate-700 text-white placeholder:text-slate-500 pl-10 pt-3"
                  />
                  <MessageSquare className="absolute left-3 top-3 w-4 h-4 text-slate-500" />
                </div>
              </div>
              
              <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                <Button className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold">
                  Send Message
                  <Send className="ml-2 h-4 w-4" />
                </Button>
              </motion.div>
              
              <p className="text-sm text-slate-500 text-center">
                By submitting this form, you agree to our privacy policy and terms of service.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
