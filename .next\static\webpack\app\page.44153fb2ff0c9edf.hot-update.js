"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/features.tsx":
/*!*********************************!*\
  !*** ./components/features.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Features: () => (/* binding */ Features)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Search,Shield,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* __next_internal_client_entry_do_not_use__ Features auto */ \n\n\n\nconst features = [\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Advanced Link Discovery\",\n        description: \"Uncover hidden link opportunities with our proprietary crawling technology that analyzes millions of websites daily.\",\n        stats: \"50M+ links discovered\",\n        color: \"from-purple-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Comprehensive Reporting\",\n        description: \"Generate detailed reports with actionable insights, custom metrics, and white-label options for agencies.\",\n        stats: \"200+ metrics tracked\",\n        color: \"from-cyan-500 to-blue-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Toxic Link Detection\",\n        description: \"Protect your site from harmful backlinks with AI-powered toxic link identification and disavow file generation.\",\n        stats: \"99.8% accuracy rate\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Lightning Fast Analysis\",\n        description: \"Get instant results with our high-performance infrastructure that processes millions of data points in seconds.\",\n        stats: \"2.1s avg response time\",\n        color: \"from-orange-500 to-red-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Competitor Intelligence\",\n        description: \"Reverse-engineer your competitors' link building strategies and discover their most valuable backlinks.\",\n        stats: \"10M+ competitors analyzed\",\n        color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Globe_Search_Shield_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Global Database\",\n        description: \"Access the world's largest link database with coverage across 190+ countries and 500+ languages.\",\n        stats: \"15B+ URLs indexed\",\n        color: \"from-pink-500 to-rose-500\"\n    }\n];\nfunction Features() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"features\",\n        className: \"py-20 bg-gradient-to-b from-slate-950 to-slate-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-6xl font-bold text-white mb-6\",\n                            children: [\n                                \"Supercharge Your\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent\",\n                                    children: \"SEO Game\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Unlock the full potential of your website with our cutting-edge SEO tools and data-driven insights\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            whileHover: {\n                                scale: 1.02,\n                                y: -5\n                            },\n                            className: \"group relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r \".concat(feature.color, \" opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        whileHover: {\n                                                            scale: 1.1,\n                                                            rotate: 5\n                                                        },\n                                                        className: \"w-16 h-16 bg-gradient-to-r \".concat(feature.color, \" rounded-2xl flex items-center justify-center shadow-lg\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-semibold bg-gradient-to-r \".concat(feature.color, \" bg-clip-text text-transparent\"),\n                                                            children: feature.stats\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 leading-relaxed text-lg group-hover:text-slate-300 transition-colors\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-slate-700/50 rounded-full h-1 overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        width: 0\n                                                    },\n                                                    whileInView: {\n                                                        width: \"100%\"\n                                                    },\n                                                    transition: {\n                                                        delay: index * 0.1 + 0.5,\n                                                        duration: 1.5\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    className: \"bg-gradient-to-r \".concat(feature.color, \" h-full rounded-full\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this)\n                        }, feature.title, false, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\features.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c = Features;\nvar _c;\n$RefreshReg$(_c, \"Features\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/features.tsx\n"));

/***/ })

});