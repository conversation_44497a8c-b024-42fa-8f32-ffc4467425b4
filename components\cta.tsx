"use client"

import React from "react"
import { motion } from "framer-motion"
import { ArrowR<PERSON>, Sparkles, Zap, Target, Star, Shield, Globe } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function CTA() {
  return (
    <section className="py-32 bg-gradient-to-br from-purple-900 via-slate-900 to-pink-900 relative overflow-hidden">
      {/* Enhanced 3D Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-purple-600/20 via-transparent to-pink-600/20"></div>

        {/* Floating 3D Orbs */}
        <motion.div
          animate={{
            rotate: 360,
            scale: [1, 1.2, 1],
            y: [-20, 20, -20]
          }}
          transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
          className="absolute top-10 right-10 w-96 h-96 bg-gradient-to-r from-purple-500/15 to-pink-500/15 rounded-full blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(168,85,247,0.15) 0%, rgba(236,72,153,0.1) 50%, transparent 100%)',
            filter: 'blur(60px)',
          }}
        />

        <motion.div
          animate={{
            rotate: -360,
            scale: [1.2, 0.8, 1.2],
            x: [-30, 30, -30]
          }}
          transition={{ duration: 40, repeat: Infinity, ease: "linear" }}
          className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-r from-cyan-500/15 to-blue-500/15 rounded-full blur-3xl"
          style={{
            background: 'radial-gradient(circle, rgba(6,182,212,0.15) 0%, rgba(59,130,246,0.1) 50%, transparent 100%)',
            filter: 'blur(50px)',
          }}
        />

        {/* Additional floating elements */}
        <motion.div
          animate={{
            rotate: [0, 180, 360],
            scale: [0.8, 1.3, 0.8],
            y: [30, -40, 30],
            x: [20, -20, 20]
          }}
          transition={{ duration: 35, repeat: Infinity, ease: "easeInOut" }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-30, -100, -30],
              opacity: [0, 1, 0],
              scale: [0, 1.5, 0],
              rotate: [0, 360, 720],
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm"
            >
              <Sparkles className="w-5 h-5 mr-3 text-purple-400" />
              <span className="text-purple-300 font-medium">Limited Time Offer</span>
            </motion.div>
            
            <h2 className="text-5xl md:text-7xl font-black text-white leading-tight">
              Ready to{" "}
              <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
                Dominate
              </span>
              <br />
              Your Market?
            </h2>
            
            <p className="text-2xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Join 250,000+ successful businesses using LinkNest to crush their competition and achieve unprecedented growth
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
              <div className="flex-1 relative">
                <Input
                  placeholder="Enter your website URL to get started..."
                  className="h-16 text-lg bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 pr-16 backdrop-blur-sm rounded-2xl"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-white" />
                  </div>
                </div>
              </div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button size="lg" className="h-16 px-10 bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 text-white font-bold text-lg shadow-2xl shadow-purple-500/25 rounded-2xl">
                  Start Free Analysis
                  <ArrowRight className="ml-3 h-6 w-6" />
                </Button>
              </motion.div>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {[
                { icon: Sparkles, text: "Free Forever Plan", subtext: "No credit card required" },
                { icon: Zap, text: "Instant Results", subtext: "Get insights in 2.1 seconds" },
                { icon: Target, text: "30-Day Money Back", subtext: "Risk-free guarantee" }
              ].map((item, index) => (
                <motion.div
                  key={item.text}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 + index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-4 bg-slate-800/30 backdrop-blur-sm rounded-2xl p-4 border border-slate-700/30"
                >
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0">
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-left">
                    <div className="text-white font-semibold">{item.text}</div>
                    <div className="text-slate-400 text-sm">{item.subtext}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-slate-400 text-lg"
          >
            Trusted by industry leaders • 4.9/5 rating • 99.9% uptime
          </motion.div>
        </div>
      </div>
    </section>
  )
}
