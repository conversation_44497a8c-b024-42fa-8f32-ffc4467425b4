"use client"

import { motion } from "framer-motion"
import { BarChart3, Shield, Zap, Target, Globe, TrendingUp, Search, Users } from 'lucide-react'

const bentoItems = [
  {
    title: "Real-time Analytics",
    description: "Monitor your backlinks and SEO metrics in real-time with live updates",
    icon: BarChart3,
    className: "md:col-span-2 md:row-span-2",
    gradient: "from-purple-500/20 to-pink-500/20",
    border: "border-purple-500/30"
  },
  {
    title: "AI-Powered Insights",
    description: "Get intelligent recommendations powered by machine learning",
    icon: Zap,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-cyan-500/20 to-blue-500/20",
    border: "border-cyan-500/30"
  },
  {
    title: "Competitor Analysis",
    description: "Spy on your competitors and discover their link building strategies",
    icon: Target,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-green-500/20 to-emerald-500/20",
    border: "border-green-500/30"
  },
  {
    title: "Global Coverage",
    description: "Analyze websites from 190+ countries with our worldwide database",
    icon: Globe,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-orange-500/20 to-red-500/20",
    border: "border-orange-500/30"
  },
  {
    title: "Link Quality Score",
    description: "Advanced algorithms to assess the quality and value of each backlink",
    icon: Shield,
    className: "md:col-span-2 md:row-span-1",
    gradient: "from-indigo-500/20 to-purple-500/20",
    border: "border-indigo-500/30"
  },
  {
    title: "Growth Tracking",
    description: "Track your SEO progress with detailed growth metrics and trends",
    icon: TrendingUp,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-pink-500/20 to-rose-500/20",
    border: "border-pink-500/30"
  }
]

export function BentoGrid() {
  return (
    <section className="py-20 bg-slate-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Everything You Need in{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              One Platform
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            Comprehensive SEO tools designed to give you the competitive edge you need
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 auto-rows-fr">
          {bentoItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className={`group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:${item.border} transition-all duration-500 ${item.className}`}
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-100 rounded-3xl transition-opacity duration-500`}></div>
              
              {/* Content */}
              <div className="relative z-10 h-full flex flex-col">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-purple-500/25"
                >
                  <item.icon className="w-8 h-8 text-white" />
                </motion.div>
                
                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors">
                  {item.title}
                </h3>
                
                <p className="text-slate-400 leading-relaxed flex-grow group-hover:text-slate-300 transition-colors">
                  {item.description}
                </p>

                {/* Decorative Elements */}
                <div className="absolute top-4 right-4 w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-4 right-4 w-1 h-1 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 delay-100"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
