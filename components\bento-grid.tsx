"use client"

import React from "react"
import { motion } from "framer-motion"
import { BarChart3, Shield, Zap, Target, Globe, TrendingUp, Search, Users, Sparkles, ArrowUpRight } from 'lucide-react'

const bentoItems = [
  {
    title: "Real-time Analytics",
    description: "Monitor your backlinks and SEO metrics in real-time with live updates",
    icon: BarChart3,
    className: "md:col-span-2 md:row-span-2",
    gradient: "from-purple-500/20 to-pink-500/20",
    border: "border-purple-500/30"
  },
  {
    title: "AI-Powered Insights",
    description: "Get intelligent recommendations powered by machine learning",
    icon: Zap,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-cyan-500/20 to-blue-500/20",
    border: "border-cyan-500/30"
  },
  {
    title: "Competitor Analysis",
    description: "Spy on your competitors and discover their link building strategies",
    icon: Target,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-green-500/20 to-emerald-500/20",
    border: "border-green-500/30"
  },
  {
    title: "Global Coverage",
    description: "Analyze websites from 190+ countries with our worldwide database",
    icon: Globe,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-orange-500/20 to-red-500/20",
    border: "border-orange-500/30"
  },
  {
    title: "Link Quality Score",
    description: "Advanced algorithms to assess the quality and value of each backlink",
    icon: Shield,
    className: "md:col-span-2 md:row-span-1",
    gradient: "from-indigo-500/20 to-purple-500/20",
    border: "border-indigo-500/30"
  },
  {
    title: "Growth Tracking",
    description: "Track your SEO progress with detailed growth metrics and trends",
    icon: TrendingUp,
    className: "md:col-span-1 md:row-span-1",
    gradient: "from-pink-500/20 to-rose-500/20",
    border: "border-pink-500/30"
  }
]

export function BentoGrid() {
  return (
    <section className="py-32 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
      {/* 3D Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-40 left-40 w-96 h-96 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            rotate: [360, 0],
            scale: [1.2, 0.8, 1.2],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-40 right-40 w-80 h-80 bg-gradient-to-r from-cyan-500/5 to-blue-500/5 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm mb-8"
          >
            <Sparkles className="w-5 h-5 text-purple-400" />
            <span className="text-purple-300 font-medium">All-in-One Platform</span>
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-black text-white mb-6 leading-tight">
            Everything You Need in{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              One Platform
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            Comprehensive SEO tools designed to give you the competitive edge you need
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 auto-rows-fr">
          {bentoItems.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 50, rotateX: -15 }}
              whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ delay: index * 0.15, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.05,
                y: -15,
                rotateX: 5,
                rotateY: 5,
                z: 50
              }}
              className={`group relative bg-gradient-to-br from-slate-800/60 to-slate-900/90 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:${item.border} transition-all duration-500 ${item.className} overflow-hidden`}
              style={{
                transformStyle: 'preserve-3d',
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
              }}
            >
              {/* Enhanced Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-15 rounded-3xl transition-opacity duration-500`}></div>
              <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-5 rounded-3xl blur-xl transition-opacity duration-500`}></div>

              {/* Floating Particles */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
                    style={{
                      left: `${15 + i * 10}%`,
                      top: `${15 + i * 8}%`,
                    }}
                    animate={{
                      y: [-10, -25, -10],
                      opacity: [0, 1, 0],
                      scale: [0, 1, 0],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: i * 0.3,
                      ease: "easeInOut",
                    }}
                  />
                ))}
              </div>

              {/* Content */}
              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-start justify-between mb-6">
                  <motion.div
                    whileHover={{
                      scale: 1.2,
                      rotate: 15,
                      z: 20
                    }}
                    className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25 relative"
                    style={{
                      transformStyle: 'preserve-3d',
                      boxShadow: '0 15px 35px -5px rgba(168, 85, 247, 0.4)',
                    }}
                  >
                    <item.icon className="w-8 h-8 text-white" />
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur-lg opacity-50 scale-110"></div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ delay: index * 0.1 + 0.5 }}
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  >
                    <ArrowUpRight className="w-5 h-5 text-slate-400" />
                  </motion.div>
                </div>

                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors">
                  {item.title}
                </h3>

                <p className="text-slate-400 leading-relaxed flex-grow group-hover:text-slate-300 transition-colors text-lg">
                  {item.description}
                </p>

                {/* Enhanced Progress Indicator */}
                <div className="mt-6 w-full bg-slate-700/50 rounded-full h-1 overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    whileInView={{ width: "100%" }}
                    transition={{ delay: index * 0.1 + 1, duration: 2 }}
                    viewport={{ once: true }}
                    className="bg-gradient-to-r from-purple-500 to-pink-500 h-full rounded-full relative"
                    style={{
                      boxShadow: '0 0 10px rgba(168, 85, 247, 0.5)',
                    }}
                  >
                    <motion.div
                      animate={{ x: [-10, 10, -10] }}
                      transition={{ duration: 2, repeat: Infinity }}
                      className="absolute right-0 top-0 w-2 h-full bg-white/40 rounded-full"
                    />
                  </motion.div>
                </div>

                {/* Enhanced Decorative Elements */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <motion.div
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="w-full h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
                  />
                </div>
                <div className="absolute bottom-6 right-6 w-2 h-2 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 delay-100">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    className="w-full h-full bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full"
                  />
                </div>

                {/* Corner Glow */}
                <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-purple-500/20 to-transparent rounded-tl-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
