"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./components/about/about-team.tsx":
/*!*****************************************!*\
  !*** ./components/about/about-team.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutTeam: () => (/* binding */ AboutTeam)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Linkedin,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ AboutTeam auto */ \n\n\nconst team = [\n    {\n        name: \"Mohammad Tayyab\",\n        role: \"Founder & CEO\",\n        bio: \"Former Google engineer with 10+ years in SEO. Built LinkNest to solve the problems he faced daily.\",\n        image: \"/placeholder.svg?height=200&width=200&text=MT\",\n        social: {\n            linkedin: \"#\",\n            twitter: \"#\",\n            github: \"#\"\n        }\n    },\n    {\n        name: \"Sarah Chen\",\n        role: \"CTO\",\n        bio: \"AI/ML expert who previously led engineering teams at major tech companies. Passionate about scalable systems.\",\n        image: \"/placeholder.svg?height=200&width=200&text=SC\",\n        social: {\n            linkedin: \"#\",\n            twitter: \"#\",\n            github: \"#\"\n        }\n    },\n    {\n        name: \"Marcus Rodriguez\",\n        role: \"Head of Product\",\n        bio: \"Product strategist with deep SEO expertise. Ensures every feature solves real customer problems.\",\n        image: \"/placeholder.svg?height=200&width=200&text=MR\",\n        social: {\n            linkedin: \"#\",\n            twitter: \"#\"\n        }\n    },\n    {\n        name: \"Emily Watson\",\n        role: \"VP of Marketing\",\n        bio: \"Growth marketing expert who's helped scale multiple SaaS companies from startup to IPO.\",\n        image: \"/placeholder.svg?height=200&width=200&text=EW\",\n        social: {\n            linkedin: \"#\",\n            twitter: \"#\"\n        }\n    }\n];\nfunction AboutTeam() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-slate-950\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Meet Our\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                    children: \"Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-400 max-w-3xl mx-auto\",\n                            children: \"The passionate individuals behind LinkNest's success\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: team.map((member, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            whileHover: {\n                                scale: 1.02,\n                                y: -5\n                            },\n                            className: \"group text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500\",\n                                style: {\n                                    background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',\n                                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: member.image || \"/placeholder.svg\",\n                                                        alt: member.name,\n                                                        className: \"w-24 h-24 rounded-2xl mx-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-400 rounded-full border-2 border-slate-900\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-white mb-1\",\n                                                        children: member.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-400 font-semibold mb-3\",\n                                                        children: member.role\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm leading-relaxed\",\n                                                        children: member.bio\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center space-x-4\",\n                                                children: [\n                                                    member.social.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: member.social.linkedin,\n                                                        className: \"w-8 h-8 bg-slate-700 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    member.social.twitter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: member.social.twitter,\n                                                        className: \"w-8 h-8 bg-slate-700 hover:bg-blue-400 rounded-lg flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    member.social.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: member.social.github,\n                                                        className: \"w-8 h-8 bg-slate-700 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Linkedin_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, member.name, false, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-team.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutTeam;\nvar _c;\n$RefreshReg$(_c, \"AboutTeam\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/about/about-team.tsx\n"));

/***/ })

});