"use client"

import React from "react"
import { motion } from "framer-motion"
import { MapPin, Clock, Globe, Headphones } from 'lucide-react'

const contactInfo = [
  {
    icon: MapPin,
    title: "Our Office",
    details: ["123 SEO Street", "Digital District, CA 90210", "United States"],
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: Clock,
    title: "Business Hours",
    details: ["Monday - Friday: 9:00 AM - 6:00 PM PST", "Saturday: 10:00 AM - 4:00 PM PST", "Sunday: Closed"],
    color: "from-cyan-500 to-blue-500"
  },
  {
    icon: Globe,
    title: "Global Presence",
    details: ["Serving 190+ countries", "24/7 technical support", "Multi-language support"],
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Headphones,
    title: "Support Channels",
    details: ["Live chat (24/7)", "Email support", "Phone support (business hours)"],
    color: "from-orange-500 to-red-500"
  }
]

export function ContactInfo() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 to-slate-950">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Get in{" "}
              <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                Touch
              </span>
            </h2>
            <p className="text-slate-400">
              Multiple ways to reach our team
            </p>
          </div>

          <div className="space-y-6">
            {contactInfo.map((info, index) => (
              <motion.div
                key={info.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02, x: 5 }}
                className="group"
              >
                <div
                  className="backdrop-blur-xl rounded-2xl p-6 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-300 relative"
                  style={{
                    background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
                  }}
                >
                  {/* Enhanced Gradient Overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${info.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}></div>
                  <div className="flex items-start space-x-4 relative z-10">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className={`w-12 h-12 bg-gradient-to-r ${info.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg`}
                    >
                      <info.icon className="w-6 h-6 text-white" />
                    </motion.div>
                    
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-purple-300 transition-colors">
                        {info.title}
                      </h3>
                      <div className="space-y-1">
                        {info.details.map((detail, i) => (
                          <p key={i} className="text-slate-400 group-hover:text-slate-300 transition-colors">
                            {detail}
                          </p>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-2xl p-6 border border-purple-500/20"
          >
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-white mb-1">&lt; 2min</div>
                <div className="text-sm text-slate-400">Avg Response Time</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-white mb-1">98%</div>
                <div className="text-sm text-slate-400">Customer Satisfaction</div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
