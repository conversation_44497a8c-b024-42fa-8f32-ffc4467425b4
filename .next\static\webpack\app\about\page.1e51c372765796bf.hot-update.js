"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./components/about/about-values.tsx":
/*!*******************************************!*\
  !*** ./components/about/about-values.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutValues: () => (/* binding */ AboutValues)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Globe,Shield,Target,Users,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ AboutValues auto */ \n\n\nconst values = [\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        title: \"Transparency\",\n        description: \"We believe in complete transparency in our methods, pricing, and data sources.\",\n        color: \"from-green-500 to-emerald-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Speed\",\n        description: \"Fast results shouldn't come at the cost of accuracy. We deliver both.\",\n        color: \"from-yellow-500 to-orange-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Community\",\n        description: \"We're building more than a tool - we're fostering a community of SEO professionals.\",\n        color: \"from-blue-500 to-cyan-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Accessibility\",\n        description: \"Powerful SEO tools should be accessible to businesses of all sizes, everywhere.\",\n        color: \"from-purple-500 to-pink-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Excellence\",\n        description: \"We set the highest standards for ourselves and continuously strive to exceed them.\",\n        color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n        icon: _barrel_optimize_names_Award_Globe_Shield_Target_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Results\",\n        description: \"Everything we do is measured by one metric: our customers' success.\",\n        color: \"from-red-500 to-pink-500\"\n    }\n];\nfunction AboutValues() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-b from-slate-950 to-slate-900\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-white mb-4\",\n                            children: [\n                                \"Our\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                                    children: \"Values\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-400 max-w-3xl mx-auto\",\n                            children: \"The principles that guide everything we do and every decision we make\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: values.map((value, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: index * 0.1,\n                                duration: 0.8\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            whileHover: {\n                                scale: 1.02,\n                                y: -5\n                            },\n                            className: \"group relative backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500\",\n                            style: {\n                                background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',\n                                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r \".concat(value.color, \" opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 text-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.1,\n                                                rotate: 5\n                                            },\n                                            className: \"w-16 h-16 bg-gradient-to-r \".concat(value.color, \" rounded-2xl flex items-center justify-center mx-auto shadow-lg\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(value.icon, {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-white group-hover:text-white transition-colors\",\n                                            children: value.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors\",\n                                            children: value.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, value.title, true, {\n                            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Moaz Coding Playground\\\\linknest-website\\\\components\\\\about\\\\about-values.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = AboutValues;\nvar _c;\n$RefreshReg$(_c, \"AboutValues\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/about/about-values.tsx\n"));

/***/ })

});