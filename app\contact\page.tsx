"use client"

import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { ContactHero } from "@/components/contact/contact-hero"
import { ContactForm } from "@/components/contact/contact-form"
import { ContactInfo } from "@/components/contact/contact-info"
import { ContactFAQ } from "@/components/contact/contact-faq"

export default function ContactPage() {
  return (
    <main className="min-h-screen bg-slate-950 overflow-x-hidden">
      <Header />
      <ContactHero />
      <div className="grid lg:grid-cols-2 gap-0">
        <ContactForm />
        <ContactInfo />
      </div>
      <ContactFAQ />
      <Footer />
    </main>
  )
}
