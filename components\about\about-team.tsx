"use client"

import { motion } from "framer-motion"
import { Linkedin, Twitter, Github } from 'lucide-react'

const team = [
  {
    name: "<PERSON>",
    role: "Founder & CEO",
    bio: "Former Google engineer with 10+ years in SEO. Built LinkNest to solve the problems he faced daily.",
    image: "/placeholder.svg?height=200&width=200&text=MT",
    social: {
      linkedin: "#",
      twitter: "#",
      github: "#"
    }
  },
  {
    name: "<PERSON>",
    role: "CTO",
    bio: "AI/ML expert who previously led engineering teams at major tech companies. Passionate about scalable systems.",
    image: "/placeholder.svg?height=200&width=200&text=SC",
    social: {
      linkedin: "#",
      twitter: "#",
      github: "#"
    }
  },
  {
    name: "<PERSON>",
    role: "Head of Product",
    bio: "Product strategist with deep SEO expertise. Ensures every feature solves real customer problems.",
    image: "/placeholder.svg?height=200&width=200&text=MR",
    social: {
      linkedin: "#",
      twitter: "#"
    }
  },
  {
    name: "<PERSON>",
    role: "VP of Marketing",
    bio: "Growth marketing expert who's helped scale multiple SaaS companies from startup to IPO.",
    image: "/placeholder.svg?height=200&width=200&text=EW",
    social: {
      linkedin: "#",
      twitter: "#"
    }
  }
]

export function AboutTeam() {
  return (
    <section className="py-20 bg-slate-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Meet Our{" "}
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Team
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto">
            The passionate individuals behind LinkNest's success
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {team.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="group text-center"
            >
              <div
                className="relative backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500"
                style={{
                  background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 50%, rgba(2, 6, 23, 0.95) 100%)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
                }}
              >
                {/* Enhanced Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className="space-y-6 relative z-10">
                  <div className="relative">
                    <img src={member.image || "/placeholder.svg"} alt={member.name} className="w-24 h-24 rounded-2xl mx-auto" />
                    <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-400 rounded-full border-2 border-slate-900"></div>
                  </div>
                  
                  <div>
                    <h3 className="text-xl font-bold text-white mb-1">{member.name}</h3>
                    <p className="text-purple-400 font-semibold mb-3">{member.role}</p>
                    <p className="text-slate-400 text-sm leading-relaxed">{member.bio}</p>
                  </div>
                  
                  <div className="flex justify-center space-x-4">
                    {member.social.linkedin && (
                      <a href={member.social.linkedin} className="w-8 h-8 bg-slate-700 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors">
                        <Linkedin className="w-4 h-4 text-white" />
                      </a>
                    )}
                    {member.social.twitter && (
                      <a href={member.social.twitter} className="w-8 h-8 bg-slate-700 hover:bg-blue-400 rounded-lg flex items-center justify-center transition-colors">
                        <Twitter className="w-4 h-4 text-white" />
                      </a>
                    )}
                    {member.social.github && (
                      <a href={member.social.github} className="w-8 h-8 bg-slate-700 hover:bg-purple-600 rounded-lg flex items-center justify-center transition-colors">
                        <Github className="w-4 h-4 text-white" />
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
