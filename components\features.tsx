"use client"

import React from "react"
import { motion } from "framer-motion"
import { Search, BarChart3, Shield, Zap, Target, Globe, Users, Award, Sparkles, ArrowUpRight } from 'lucide-react'

const features = [
  {
    icon: Search,
    title: "Advanced Link Discovery",
    description: "Uncover hidden link opportunities with our proprietary crawling technology that analyzes millions of websites daily.",
    stats: "50M+ links discovered",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: BarChart3,
    title: "Comprehensive Reporting",
    description: "Generate detailed reports with actionable insights, custom metrics, and white-label options for agencies.",
    stats: "200+ metrics tracked",
    color: "from-cyan-500 to-blue-500"
  },
  {
    icon: Shield,
    title: "Toxic Link Detection",
    description: "Protect your site from harmful backlinks with AI-powered toxic link identification and disavow file generation.",
    stats: "99.8% accuracy rate",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Zap,
    title: "Lightning Fast Analysis",
    description: "Get instant results with our high-performance infrastructure that processes millions of data points in seconds.",
    stats: "2.1s avg response time",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Target,
    title: "Competitor Intelligence",
    description: "Reverse-engineer your competitors' link building strategies and discover their most valuable backlinks.",
    stats: "10M+ competitors analyzed",
    color: "from-indigo-500 to-purple-500"
  },
  {
    icon: Globe,
    title: "Global Database",
    description: "Access the world's largest link database with coverage across 190+ countries and 500+ languages.",
    stats: "15B+ URLs indexed",
    color: "from-pink-500 to-rose-500"
  }
]

export function Features() {
  return (
    <section id="features" className="py-32 bg-gradient-to-b from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
      {/* 3D Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            rotate: [360, 0],
            scale: [1.2, 0.8, 1.2],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-3xl"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 backdrop-blur-sm mb-8"
          >
            <Sparkles className="w-5 h-5 text-purple-400" />
            <span className="text-purple-300 font-medium">Powered by AI</span>
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-black text-white mb-6 leading-tight">
            Supercharge Your{" "}
            <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
              SEO Game
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            Unlock the full potential of your website with our cutting-edge SEO tools and data-driven insights
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 50, rotateX: -15 }}
              whileInView={{ opacity: 1, y: 0, rotateX: 0 }}
              transition={{ delay: index * 0.15, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{
                scale: 1.03,
                y: -10,
                rotateX: 5,
                rotateY: 5,
                z: 50
              }}
              className="group relative"
              style={{ transformStyle: 'preserve-3d' }}
            >
              <div className="relative bg-gradient-to-br from-slate-800/60 to-slate-900/90 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/40 transition-all duration-500 h-full overflow-hidden"
                style={{
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
                }}
              >
                {/* Enhanced Glow Effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-10 rounded-3xl transition-opacity duration-500`}></div>
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-5 rounded-3xl blur-xl transition-opacity duration-500`}></div>

                {/* Floating Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      className={`absolute w-1 h-1 bg-gradient-to-r ${feature.color} rounded-full`}
                      style={{
                        left: `${20 + i * 15}%`,
                        top: `${20 + i * 10}%`,
                      }}
                      animate={{
                        y: [-10, -20, -10],
                        opacity: [0, 1, 0],
                        scale: [0, 1, 0],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: "easeInOut",
                      }}
                    />
                  ))}
                </div>

                <div className="relative z-10 space-y-6">
                  <div className="flex items-start justify-between">
                    <motion.div
                      whileHover={{
                        scale: 1.2,
                        rotate: 15,
                        z: 20
                      }}
                      className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center shadow-lg relative`}
                      style={{
                        transformStyle: 'preserve-3d',
                        boxShadow: `0 10px 30px -5px rgba(168, 85, 247, 0.3)`,
                      }}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                      <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} rounded-2xl blur-lg opacity-50 scale-110`}></div>
                    </motion.div>

                    <div className="text-right">
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        className={`text-sm font-bold bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`}
                      >
                        {feature.stats}
                      </motion.div>
                      <motion.div
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: index * 0.1 + 0.5 }}
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      >
                        <ArrowUpRight className="w-4 h-4 text-slate-400 mt-1" />
                      </motion.div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-slate-400 leading-relaxed text-lg group-hover:text-slate-300 transition-colors">
                      {feature.description}
                    </p>
                  </div>

                  {/* Enhanced Progress Bar */}
                  <div className="w-full bg-slate-700/50 rounded-full h-2 overflow-hidden backdrop-blur-sm">
                    <motion.div
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      transition={{ delay: index * 0.1 + 0.8, duration: 2, ease: "easeOut" }}
                      viewport={{ once: true }}
                      className={`bg-gradient-to-r ${feature.color} h-full rounded-full relative`}
                      style={{
                        boxShadow: `0 0 20px rgba(168, 85, 247, 0.4)`,
                      }}
                    >
                      <motion.div
                        animate={{ x: [-20, 20, -20] }}
                        transition={{ duration: 3, repeat: Infinity }}
                        className="absolute right-0 top-0 w-4 h-full bg-white/30 rounded-full"
                      />
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Decorative Elements */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <motion.div
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="w-full h-full bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"
                  />
                </div>

                {/* Corner Accent */}
                <div className="absolute bottom-6 left-6 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                  <div className={`w-8 h-8 border-2 border-gradient-to-r ${feature.color} rounded-full`}></div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
