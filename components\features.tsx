"use client"

import { motion } from "framer-motion"
import { Search, BarChart3, Shield, Zap, Target, Globe, Users, Award } from 'lucide-react'

const features = [
  {
    icon: Search,
    title: "Advanced Link Discovery",
    description: "Uncover hidden link opportunities with our proprietary crawling technology that analyzes millions of websites daily.",
    stats: "50M+ links discovered",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: BarChart3,
    title: "Comprehensive Reporting",
    description: "Generate detailed reports with actionable insights, custom metrics, and white-label options for agencies.",
    stats: "200+ metrics tracked",
    color: "from-cyan-500 to-blue-500"
  },
  {
    icon: Shield,
    title: "Toxic Link Detection",
    description: "Protect your site from harmful backlinks with AI-powered toxic link identification and disavow file generation.",
    stats: "99.8% accuracy rate",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Zap,
    title: "Lightning Fast Analysis",
    description: "Get instant results with our high-performance infrastructure that processes millions of data points in seconds.",
    stats: "2.1s avg response time",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Target,
    title: "Competitor Intelligence",
    description: "Reverse-engineer your competitors' link building strategies and discover their most valuable backlinks.",
    stats: "10M+ competitors analyzed",
    color: "from-indigo-500 to-purple-500"
  },
  {
    icon: Globe,
    title: "Global Database",
    description: "Access the world's largest link database with coverage across 190+ countries and 500+ languages.",
    stats: "15B+ URLs indexed",
    color: "from-pink-500 to-rose-500"
  }
]

export function Features() {
  return (
    <section id="features" className="py-20 bg-gradient-to-b from-slate-950 to-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Supercharge Your{" "}
            <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
              SEO Game
            </span>
          </h2>
          <p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
            Unlock the full potential of your website with our cutting-edge SEO tools and data-driven insights
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02, y: -5 }}
              className="group relative"
            >
              <div className="relative bg-gradient-to-br from-slate-800/50 to-slate-900/80 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-500 h-full">
                {/* Glow Effect */}
                <div className={`absolute inset-0 bg-gradient-to-r ${feature.color} opacity-0 group-hover:opacity-5 rounded-3xl transition-opacity duration-500`}></div>
                
                <div className="relative z-10 space-y-6">
                  <div className="flex items-start justify-between">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center shadow-lg`}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </motion.div>
                    
                    <div className="text-right">
                      <div className={`text-sm font-semibold bg-gradient-to-r ${feature.color} bg-clip-text text-transparent`}>
                        {feature.stats}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors">
                      {feature.title}
                    </h3>
                    <p className="text-slate-400 leading-relaxed text-lg group-hover:text-slate-300 transition-colors">
                      {feature.description}
                    </p>
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-slate-700/50 rounded-full h-1 overflow-hidden">
                    <motion.div
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      transition={{ delay: index * 0.1 + 0.5, duration: 1.5 }}
                      viewport={{ once: true }}
                      className={`bg-gradient-to-r ${feature.color} h-full rounded-full`}
                    ></motion.div>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-6 right-6 w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
